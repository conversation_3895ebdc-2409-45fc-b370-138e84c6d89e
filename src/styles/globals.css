@import './design-tokens.css';
@import "tailwindcss";

/*
 * Clean Design Token System
 * All defaults are defined in design-tokens.css
 * This file only contains the base color palette and semantic mappings
 */
@theme {
  /* ===== BASE COLOR PALETTE ===== */
  /* OKCH colors for better perceptual uniformity */

  /* Gray Scale */
  --gray-25: oklch(99% 0.002 247);
  --gray-50: oklch(97% 0.003 247);
  --gray-75: oklch(95% 0.004 247);
  --gray-100: oklch(92% 0.005 247);
  --gray-200: oklch(87% 0.007 247);
  --gray-300: oklch(78% 0.012 247);
  --gray-400: oklch(65% 0.015 247);
  --gray-500: oklch(52% 0.012 247);
  --gray-600: oklch(42% 0.010 247);
  --gray-700: oklch(32% 0.008 247);
  --gray-800: oklch(22% 0.006 247);
  --gray-900: oklch(15% 0.004 247);
  --gray-950: oklch(10% 0.002 247);

  /* Blue Scale */
  --blue-100: oklch(95% 0.025 250);
  --blue-200: oklch(87% 0.045 250);
  --blue-300: oklch(75% 0.075 250);
  --blue-400: oklch(62% 0.125 250);
  --blue-500: oklch(55% 0.155 250);
  --blue-600: oklch(45% 0.135 250);
  --blue-700: oklch(35% 0.115 250);
  --blue-800: oklch(25% 0.095 250);
  --blue-900: oklch(15% 0.075 250);

  /* Green Scale */
  --green-100: oklch(94% 0.035 145);
  --green-200: oklch(85% 0.065 145);
  --green-300: oklch(75% 0.095 145);
  --green-400: oklch(65% 0.115 145);
  --green-500: oklch(55% 0.125 145);
  --green-600: oklch(45% 0.105 145);
  --green-700: oklch(35% 0.085 145);
  --green-800: oklch(25% 0.065 145);
  --green-900: oklch(15% 0.045 145);

  /* Red Scale */
  --red-100: oklch(94% 0.025 25);
  --red-200: oklch(82% 0.055 25);
  --red-300: oklch(70% 0.085 25);
  --red-400: oklch(62% 0.125 25);
  --red-500: oklch(52% 0.145 25);
  --red-600: oklch(42% 0.125 25);
  --red-700: oklch(32% 0.105 25);
  --red-800: oklch(22% 0.085 25);
  --red-900: oklch(15% 0.065 25);

  /* Orange Scale */
  --orange-100: oklch(96% 0.025 65);
  --orange-200: oklch(87% 0.055 65);
  --orange-300: oklch(75% 0.095 65);
  --orange-400: oklch(68% 0.125 65);
  --orange-500: oklch(62% 0.145 65);
  --orange-600: oklch(52% 0.125 65);
  --orange-700: oklch(42% 0.105 65);
  --orange-800: oklch(32% 0.085 65);
  --orange-900: oklch(22% 0.065 65);

  /* ===== SEMANTIC TOKENS - Light Theme ===== */
  /* Using design token defaults from design-tokens.css */

  /* Background Colors */
  --background-base: var(--gray-25);
  --background-layer-1: var(--gray-50);
  --background-layer-2: var(--gray-75);

  /* Surface Colors */
  --surface-base: oklch(100% 0 0);
  --surface-raised: oklch(100% 0 0);
  --surface-overlay: oklch(100% 0 0);

  /* Text Colors */
  --text-primary: var(--gray-900);
  --text-secondary: var(--gray-700);
  --text-tertiary: var(--gray-500);
  --text-disabled: var(--gray-400);
  --text-inverse: oklch(100% 0 0);

  /* Border Colors */
  --border-base: var(--gray-200);
  --border-strong: var(--gray-300);
  --border-subtle: var(--gray-100);

  /* Interactive Colors - Green Primary */
  --accent-content-default: var(--green-600);
  --accent-content-hover: var(--green-700);
  --accent-content-down: var(--green-800);
  --accent-content-focus: var(--green-600);

  --accent-background-default: var(--green-600);
  --accent-background-hover: var(--green-700);
  --accent-background-down: var(--green-800);

  --accent-border-default: var(--green-600);
  --accent-border-hover: var(--green-700);
  --accent-border-focus: var(--green-600);

  /* Semantic Status Colors */
  --positive-content: var(--green-600);
  --positive-background: var(--green-100);
  --positive-border: var(--green-300);

  --negative-content: var(--red-600);
  --negative-background: var(--red-100);
  --negative-border: var(--red-300);

  --notice-content: var(--orange-600);
  --notice-background: var(--orange-100);
  --notice-border: var(--orange-300);

  --informative-content: var(--blue-600);
  --informative-background: var(--blue-100);
  --informative-border: var(--blue-300);

  /* ===== DESIGN TOKEN MAPPINGS ===== */
  /* All spacing, typography, and other tokens are defined in design-tokens.css */
  /* This section only contains essential semantic color mappings */








}

/* ===== DARK THEME OVERRIDES ===== */
.dark {
  /* Background Colors */
  --background-base: var(--gray-950);
  --background-layer-1: var(--gray-900);
  --background-layer-2: var(--gray-800);

  /* Surface Colors */
  --surface-base: var(--gray-900);
  --surface-raised: var(--gray-800);
  --surface-overlay: var(--gray-800);

  /* Text Colors */
  --text-primary: var(--gray-50);
  --text-secondary: var(--gray-300);
  --text-tertiary: var(--gray-400);
  --text-disabled: var(--gray-500);
  --text-inverse: var(--gray-900);

  /* Border Colors */
  --border-base: var(--gray-700);
  --border-strong: var(--gray-600);
  --border-subtle: var(--gray-800);

  /* Interactive Colors - Green Primary for Dark Mode */
  --accent-content-default: var(--green-400);
  --accent-content-hover: var(--green-300);
  --accent-content-down: var(--green-500);
  --accent-content-focus: var(--green-400);

  --accent-background-default: var(--green-600);
  --accent-background-hover: var(--green-500);
  --accent-background-down: var(--green-700);

  --accent-border-default: var(--green-500);
  --accent-border-hover: var(--green-400);
  --accent-border-focus: var(--green-400);

  /* Semantic Status Colors */
  --positive-content: var(--green-400);
  --positive-background: var(--green-900);
  --positive-border: var(--green-700);

  --negative-content: var(--red-400);
  --negative-background: var(--red-900);
  --negative-border: var(--red-700);

  --notice-content: var(--orange-400);
  --notice-background: var(--orange-900);
  --notice-border: var(--orange-700);

  --informative-content: var(--blue-400);
  --informative-background: var(--blue-900);
  --informative-border: var(--blue-700);
}

/* ===== SYSTEM PREFERENCE DARK MODE ===== */
@media (prefers-color-scheme: dark) {
  :root:not([class]) {
    /* Background Colors */
    --background-base: var(--gray-950);
    --background-layer-1: var(--gray-900);
    --background-layer-2: var(--gray-800);

    /* Surface Colors */
    --surface-base: var(--gray-900);
    --surface-raised: var(--gray-800);
    --surface-overlay: var(--gray-800);

    /* Text Colors */
    --text-primary: var(--gray-50);
    --text-secondary: var(--gray-300);
    --text-tertiary: var(--gray-400);
    --text-disabled: var(--gray-500);
    --text-inverse: var(--gray-900);

    /* Border Colors */
    --border-base: var(--gray-700);
    --border-strong: var(--gray-600);
    --border-subtle: var(--gray-800);

    /* Interactive Colors - Green Primary for System Dark Mode */
    --accent-content-default: var(--green-400);
    --accent-content-hover: var(--green-300);
    --accent-content-down: var(--green-500);
    --accent-content-focus: var(--green-400);

    --accent-background-default: var(--green-600);
    --accent-background-hover: var(--green-500);
    --accent-background-down: var(--green-700);

    --accent-border-default: var(--green-500);
    --accent-border-hover: var(--green-400);
    --accent-border-focus: var(--green-400);

    /* Semantic Status Colors */
    --positive-content: var(--green-400);
    --positive-background: var(--green-900);
    --positive-border: var(--green-700);

    --negative-content: var(--red-400);
    --negative-background: var(--red-900);
    --negative-border: var(--red-700);

    --notice-content: var(--orange-400);
    --notice-background: var(--orange-900);
    --notice-border: var(--orange-700);

    --informative-content: var(--blue-400);
    --informative-background: var(--blue-900);
    --informative-border: var(--blue-700);
  }
}

/* Base styling for the document */
body {
  background-color: var(--background-base);
  color: var(--text-primary);
  font-family: "Geist", ui-sans-serif, system-ui, sans-serif;
  line-height: 1.6;
}