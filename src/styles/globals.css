@import './design-tokens.css';
@import "tailwindcss";

/*
 * Adobe Spectrum-inspired Design Token System
 * Following semantic naming conventions and hierarchical structure
 */
@theme {
  /* ===== GLOBAL TOKENS ===== */

  /* Color Palette - OKCH colors for better perceptual uniformity */
  --color-gray-25: oklch(99% 0.002 247);
  --color-gray-50: oklch(97% 0.003 247);
  --color-gray-75: oklch(95% 0.004 247);
  --color-gray-100: oklch(92% 0.005 247);
  --color-gray-200: oklch(87% 0.007 247);
  --color-gray-300: oklch(78% 0.012 247);
  --color-gray-400: oklch(65% 0.015 247);
  --color-gray-500: oklch(52% 0.012 247);
  --color-gray-600: oklch(42% 0.010 247);
  --color-gray-700: oklch(32% 0.008 247);
  --color-gray-800: oklch(22% 0.006 247);
  --color-gray-900: oklch(15% 0.004 247);
  --color-gray-950: oklch(10% 0.002 247);

  --color-blue-100: oklch(95% 0.025 250);
  --color-blue-200: oklch(87% 0.045 250);
  --color-blue-300: oklch(75% 0.075 250);
  --color-blue-400: oklch(62% 0.125 250);
  --color-blue-500: oklch(55% 0.155 250);
  --color-blue-600: oklch(45% 0.135 250);
  --color-blue-700: oklch(35% 0.115 250);
  --color-blue-800: oklch(25% 0.095 250);
  --color-blue-900: oklch(15% 0.075 250);

  --color-green-100: oklch(94% 0.035 145);
  --color-green-200: oklch(85% 0.065 145);
  --color-green-300: oklch(75% 0.095 145);
  --color-green-400: oklch(65% 0.115 145);
  --color-green-500: oklch(55% 0.125 145);
  --color-green-600: oklch(45% 0.105 145);
  --color-green-700: oklch(35% 0.085 145);
  --color-green-800: oklch(25% 0.065 145);
  --color-green-900: oklch(15% 0.045 145);

  --color-red-100: oklch(94% 0.025 25);
  --color-red-200: oklch(82% 0.055 25);
  --color-red-300: oklch(70% 0.085 25);
  --color-red-400: oklch(62% 0.125 25);
  --color-red-500: oklch(52% 0.145 25);
  --color-red-600: oklch(42% 0.125 25);
  --color-red-700: oklch(32% 0.105 25);
  --color-red-800: oklch(22% 0.085 25);
  --color-red-900: oklch(15% 0.065 25);

  --color-orange-100: oklch(96% 0.025 65);
  --color-orange-200: oklch(87% 0.055 65);
  --color-orange-300: oklch(75% 0.095 65);
  --color-orange-400: oklch(68% 0.125 65);
  --color-orange-500: oklch(62% 0.145 65);
  --color-orange-600: oklch(52% 0.125 65);
  --color-orange-700: oklch(42% 0.105 65);
  --color-orange-800: oklch(32% 0.085 65);
  --color-orange-900: oklch(22% 0.065 65);

  /* ===== SEMANTIC TOKENS - Light Theme ===== */

  /* Background Colors */
  --color-background-base: var(--color-gray-25);
  --color-background-layer-1: var(--color-gray-50);
  --color-background-layer-2: var(--color-gray-75);

  /* Surface Colors */
  --color-surface-base: oklch(100% 0 0);
  --color-surface-raised: oklch(100% 0 0);
  --color-surface-overlay: oklch(100% 0 0);

  /* Text Colors */
  --color-text-primary: var(--color-gray-900);
  --color-text-secondary: var(--color-gray-700);
  --color-text-tertiary: var(--color-gray-500);
  --color-text-disabled: var(--color-gray-400);
  --color-text-inverse: oklch(100% 0 0);

  /* Border Colors */
  --color-border-base: var(--color-gray-200);
  --color-border-strong: var(--color-gray-300);
  --color-border-subtle: var(--color-gray-100);

  /* Interactive Colors - Green Primary */
  --color-accent-content-default: var(--color-green-600);
  --color-accent-content-hover: var(--color-green-700);
  --color-accent-content-down: var(--color-green-800);
  --color-accent-content-focus: var(--color-green-600);

  --color-accent-background-default: var(--color-green-600);
  --color-accent-background-hover: var(--color-green-700);
  --color-accent-background-down: var(--color-green-800);

  --color-accent-border-default: var(--color-green-600);
  --color-accent-border-hover: var(--color-green-700);
  --color-accent-border-focus: var(--color-blue-600);

  /* Semantic Status Colors */
  --color-positive-content-default: var(--color-green-600);
  --color-positive-background-default: var(--color-green-100);
  --color-positive-border-default: var(--color-green-300);

  --color-negative-content-default: var(--color-red-600);
  --color-negative-background-default: var(--color-red-100);
  --color-negative-border-default: var(--color-red-300);

  --color-notice-content-default: var(--color-orange-600);
  --color-notice-background-default: var(--color-orange-100);
  --color-notice-border-default: var(--color-orange-300);

  --color-informative-content-default: var(--color-blue-600);
  --color-informative-background-default: var(--color-blue-100);
  --color-informative-border-default: var(--color-blue-300);

  /* Legacy mappings for backward compatibility */
  --color-background: var(--color-background-base);
  --color-foreground: var(--color-text-primary);
  --color-card: var(--color-surface-base);
  --color-card-foreground: var(--color-text-primary);
  --color-popover: var(--color-surface-overlay);
  --color-popover-foreground: var(--color-text-primary);
  --color-muted: var(--color-background-layer-1);
  --color-muted-foreground: var(--color-text-secondary);
  --color-border: var(--color-border-base);
  --color-input: var(--color-border-base);
  --color-ring: var(--color-accent-border-focus);
  --color-primary: var(--color-accent-background-default);
  --color-primary-foreground: var(--color-text-inverse);
  --color-secondary: var(--color-background-layer-1);
  --color-secondary-foreground: var(--color-text-primary);
  --color-accent: var(--color-background-layer-1);
  --color-accent-foreground: var(--color-text-primary);
  --color-destructive: var(--color-negative-content-default);
  --color-destructive-foreground: var(--color-text-inverse);
  --color-success: var(--color-positive-content-default);
  --color-success-foreground: var(--color-text-inverse);
  --color-warning: var(--color-notice-content-default);
  --color-warning-foreground: var(--color-text-inverse);
  --color-info: var(--color-informative-content-default);
  --color-info-foreground: var(--color-text-inverse);

  /* ===== SPACING TOKENS ===== */
  /* Following 4px base unit system */
  --spacing-25: 0.125rem;
  /* 2px */
  --spacing-50: 0.25rem;
  /* 4px */
  --spacing-75: 0.375rem;
  /* 6px */
  --spacing-100: 0.5rem;
  /* 8px */
  --spacing-125: 0.625rem;
  /* 10px */
  --spacing-150: 0.75rem;
  /* 12px */
  --spacing-200: 1rem;
  /* 16px */
  --spacing-250: 1.25rem;
  /* 20px */
  --spacing-300: 1.5rem;
  /* 24px */
  --spacing-400: 2rem;
  /* 32px */
  --spacing-500: 2.5rem;
  /* 40px */
  --spacing-600: 3rem;
  /* 48px */
  --spacing-700: 3.5rem;
  /* 56px */
  --spacing-800: 4rem;
  /* 64px */
  --spacing-900: 4.5rem;
  /* 72px */
  --spacing-1000: 5rem;
  /* 80px */

  /* ===== SIZING TOKENS ===== */
  --size-25: 0.125rem;
  /* 2px */
  --size-50: 0.25rem;
  /* 4px */
  --size-75: 0.375rem;
  /* 6px */
  --size-100: 0.5rem;
  /* 8px */
  --size-125: 0.625rem;
  /* 10px */
  --size-150: 0.75rem;
  /* 12px */
  --size-200: 1rem;
  /* 16px */
  --size-250: 1.25rem;
  /* 20px */
  --size-300: 1.5rem;
  /* 24px */
  --size-400: 2rem;
  /* 32px */
  --size-500: 2.5rem;
  /* 40px */
  --size-600: 3rem;
  /* 48px */
  --size-700: 3.5rem;
  /* 56px */
  --size-800: 4rem;
  /* 64px */
  --size-900: 4.5rem;
  /* 72px */
  --size-1000: 5rem;
  /* 80px */

  /* Component-specific sizes */
  --size-button-height-small: 2rem;
  /* 32px */
  --size-button-height-medium: 2.5rem;
  /* 40px */
  --size-button-height-large: 3rem;
  /* 48px */

  --size-input-height-small: 2rem;
  /* 32px */
  --size-input-height-medium: 2.5rem;
  /* 40px */
  --size-input-height-large: 3rem;
  /* 48px */

  /* ===== BORDER RADIUS TOKENS - More Rounded ===== */
  --border-radius-none: 0;
  --border-radius-small: 0.5rem;
  /* 8px */
  --border-radius-medium: 0.75rem;
  /* 12px */
  --border-radius-large: 1rem;
  /* 16px */
  --border-radius-extra-large: 1.25rem;
  /* 20px */
  --border-radius-full: 9999px;

  /* ===== DEFAULT COMPONENT RADIUS TOKENS ===== */
  --radius-default: var(--border-radius-medium);
  /* Default for most components */
  --radius-button: var(--border-radius-large);
  /* Buttons */
  --radius-card: var(--border-radius-extra-large);
  /* Cards */
  --radius-input: var(--border-radius-medium);
  /* Form inputs */
  --radius-badge: var(--border-radius-large);
  /* Badges */
  --radius-progress: var(--border-radius-small);
  /* Progress bars */

  /* ===== BORDER WIDTH TOKENS ===== */
  --border-width-none: 0;
  --border-width-thin: 1px;
  --border-width-thick: 2px;
  --border-width-thicker: 4px;

  /* ===== SHADOW TOKENS ===== */
  --shadow-none: none;
  --shadow-small: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-medium: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-large: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-extra-large: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-inner: inset 0 2px 4px 0 rgb(0 0 0 / 0.05);

  /* ===== TYPOGRAPHY TOKENS ===== */
  --font-size-50: 0.75rem;
  /* 12px */
  --font-size-75: 0.875rem;
  /* 14px */
  --font-size-100: 1rem;
  /* 16px */
  --font-size-200: 1.125rem;
  /* 18px */
  --font-size-300: 1.25rem;
  /* 20px */
  --font-size-400: 1.5rem;
  /* 24px */
  --font-size-500: 1.875rem;
  /* 30px */
  --font-size-600: 2.25rem;
  /* 36px */
  --font-size-700: 3rem;
  /* 48px */
  --font-size-800: 3.75rem;
  /* 60px */
  --font-size-900: 4.5rem;
  /* 72px */

  --line-height-100: 1;
  --line-height-125: 1.25;
  --line-height-150: 1.5;
  --line-height-175: 1.75;
  --line-height-200: 2;

  --font-weight-light: 300;
  --font-weight-regular: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;

  /* ===== RESPONSIVE TYPOGRAPHY TOKENS ===== */
  /* Mobile-first approach with responsive scaling */
  --font-size-responsive-xs: clamp(0.75rem, 0.7rem + 0.25vw, 0.875rem);
  /* 12px - 14px */
  --font-size-responsive-sm: clamp(0.875rem, 0.8rem + 0.375vw, 1rem);
  /* 14px - 16px */
  --font-size-responsive-base: clamp(1rem, 0.9rem + 0.5vw, 1.125rem);
  /* 16px - 18px */
  --font-size-responsive-lg: clamp(1.125rem, 1rem + 0.625vw, 1.25rem);
  /* 18px - 20px */
  --font-size-responsive-xl: clamp(1.25rem, 1.1rem + 0.75vw, 1.5rem);
  /* 20px - 24px */
  --font-size-responsive-2xl: clamp(1.5rem, 1.3rem + 1vw, 1.875rem);
  /* 24px - 30px */
  --font-size-responsive-3xl: clamp(1.875rem, 1.6rem + 1.375vw, 2.25rem);
  /* 30px - 36px */
  --font-size-responsive-4xl: clamp(2.25rem, 1.9rem + 1.75vw, 3rem);
  /* 36px - 48px */
  --font-size-responsive-5xl: clamp(3rem, 2.5rem + 2.5vw, 4rem);
  /* 48px - 64px */

  /* ===== ANIMATION TOKENS ===== */
  --animation-duration-fast: 150ms;
  --animation-duration-medium: 200ms;
  --animation-duration-slow: 300ms;
  --animation-duration-slower: 500ms;

  --animation-easing-ease-in: cubic-bezier(0.4, 0, 1, 1);
  --animation-easing-ease-out: cubic-bezier(0, 0, 0.2, 1);
  --animation-easing-ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --animation-easing-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);

  /* ===== FOCUS TOKENS ===== */
  --focus-ring-width: 2px;
  --focus-ring-offset: 2px;
  --focus-ring-color: var(--color-accent-border-focus);

  /* ===== Z-INDEX TOKENS ===== */
  --z-index-dropdown: 1000;
  --z-index-sticky: 1020;
  --z-index-fixed: 1030;
  --z-index-modal-backdrop: 1040;
  --z-index-modal: 1050;
  --z-index-popover: 1060;
  --z-index-tooltip: 1070;
  --z-index-toast: 1080;
}

/* ===== DARK THEME OVERRIDES ===== */
.dark {
  /* Background Colors */
  --color-background-base: var(--color-gray-950);
  --color-background-layer-1: var(--color-gray-900);
  --color-background-layer-2: var(--color-gray-800);

  /* Surface Colors */
  --color-surface-base: var(--color-gray-900);
  --color-surface-raised: var(--color-gray-800);
  --color-surface-overlay: var(--color-gray-800);

  /* Text Colors */
  --color-text-primary: var(--color-gray-50);
  --color-text-secondary: var(--color-gray-300);
  --color-text-tertiary: var(--color-gray-400);
  --color-text-disabled: var(--color-gray-500);
  --color-text-inverse: var(--color-gray-900);

  /* Border Colors */
  --color-border-base: var(--color-gray-700);
  --color-border-strong: var(--color-gray-600);
  --color-border-subtle: var(--color-gray-800);

  /* Interactive Colors */
  --color-accent-content-default: var(--color-blue-400);
  --color-accent-content-hover: var(--color-blue-300);
  --color-accent-content-down: var(--color-blue-500);
  --color-accent-content-focus: var(--color-blue-400);

  --color-accent-background-default: var(--color-blue-600);
  --color-accent-background-hover: var(--color-blue-500);
  --color-accent-background-down: var(--color-blue-700);

  --color-accent-border-default: var(--color-blue-500);
  --color-accent-border-hover: var(--color-blue-400);
  --color-accent-border-focus: var(--color-blue-400);

  /* Semantic Status Colors */
  --color-positive-content-default: var(--color-green-400);
  --color-positive-background-default: var(--color-green-900);
  --color-positive-border-default: var(--color-green-700);

  --color-negative-content-default: var(--color-red-400);
  --color-negative-background-default: var(--color-red-900);
  --color-negative-border-default: var(--color-red-700);

  --color-notice-content-default: var(--color-orange-400);
  --color-notice-background-default: var(--color-orange-900);
  --color-notice-border-default: var(--color-orange-700);

  --color-informative-content-default: var(--color-blue-400);
  --color-informative-background-default: var(--color-blue-900);
  --color-informative-border-default: var(--color-blue-700);
}

/* ===== SYSTEM PREFERENCE DARK MODE ===== */
@media (prefers-color-scheme: dark) {
  :root:not([class]) {
    /* Background Colors */
    --color-background-base: var(--color-gray-950);
    --color-background-layer-1: var(--color-gray-900);
    --color-background-layer-2: var(--color-gray-800);

    /* Surface Colors */
    --color-surface-base: var(--color-gray-900);
    --color-surface-raised: var(--color-gray-800);
    --color-surface-overlay: var(--color-gray-800);

    /* Text Colors */
    --color-text-primary: var(--color-gray-50);
    --color-text-secondary: var(--color-gray-300);
    --color-text-tertiary: var(--color-gray-400);
    --color-text-disabled: var(--color-gray-500);
    --color-text-inverse: var(--color-gray-900);

    /* Border Colors */
    --color-border-base: var(--color-gray-700);
    --color-border-strong: var(--color-gray-600);
    --color-border-subtle: var(--color-gray-800);

    /* Interactive Colors */
    --color-accent-content-default: var(--color-blue-400);
    --color-accent-content-hover: var(--color-blue-300);
    --color-accent-content-down: var(--color-blue-500);
    --color-accent-content-focus: var(--color-blue-400);

    --color-accent-background-default: var(--color-blue-600);
    --color-accent-background-hover: var(--color-blue-500);
    --color-accent-background-down: var(--color-blue-700);

    --color-accent-border-default: var(--color-blue-500);
    --color-accent-border-hover: var(--color-blue-400);
    --color-accent-border-focus: var(--color-blue-400);

    /* Semantic Status Colors */
    --color-positive-content-default: var(--color-green-400);
    --color-positive-background-default: var(--color-green-900);
    --color-positive-border-default: var(--color-green-700);

    --color-negative-content-default: var(--color-red-400);
    --color-negative-background-default: var(--color-red-900);
    --color-negative-border-default: var(--color-red-700);

    --color-notice-content-default: var(--color-orange-400);
    --color-notice-background-default: var(--color-orange-900);
    --color-notice-border-default: var(--color-orange-700);

    --color-informative-content-default: var(--color-blue-400);
    --color-informative-background-default: var(--color-blue-900);
    --color-informative-border-default: var(--color-blue-700);
  }
}

/* Base styling for the document */
body {
  background-color: var(--color-background);
  color: var(--color-foreground);
  font-family: "Geist", ui-sans-serif, system-ui, sans-serif;
  line-height: 1.6;
}