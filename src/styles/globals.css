@import "tailwindcss";

/* 
 * Design System Theme Variables
 * These automatically create utility classes like bg-background, text-foreground, etc.
 */
@theme {
  /* Base colors (light mode defaults) */
  --color-background: theme(colors.neutral.50);
  --color-foreground: theme(colors.neutral.950);
  --color-card: theme(colors.neutral.50);
  --color-card-foreground: theme(colors.neutral.950);
  --color-popover: theme(colors.neutral.50);
  --color-popover-foreground: theme(colors.neutral.950);

  /* Muted colors */
  --color-muted: theme(colors.neutral.100);
  --color-muted-foreground: theme(colors.neutral.500);

  /* Interactive elements */
  --color-border: theme(colors.neutral.200);
  --color-input: theme(colors.neutral.200);
  --color-ring: theme(colors.green.600);

  /* Primary brand colors */
  --color-primary: theme(colors.green.600);
  --color-primary-muted: theme(colors.green.500);
  --color-primary-foreground: theme(colors.neutral.50);

  /* Secondary colors */
  --color-secondary: theme(colors.neutral.100);
  --color-secondary-muted: theme(colors.neutral.200);
  --color-secondary-foreground: theme(colors.neutral.900);

  /* Accent colors */
  --color-accent: theme(colors.neutral.100);
  --color-accent-muted: theme(colors.neutral.200);
  --color-accent-foreground: theme(colors.neutral.900);

  /* Destructive/error colors */
  --color-destructive: theme(colors.red.500);
  --color-destructive-muted: theme(colors.red.400);
  --color-destructive-foreground: theme(colors.neutral.50);

  /* Additional semantic colors */
  --color-placeholder: theme(colors.neutral.400);
  --color-success: theme(colors.green.600);
  --color-success-muted: theme(colors.green.500);
  --color-success-foreground: theme(colors.neutral.50);
  --color-warning: theme(colors.amber.500);
  --color-warning-muted: theme(colors.amber.400);
  --color-warning-foreground: theme(colors.neutral.50);
  --color-info: theme(colors.blue.500);
  --color-info-muted: theme(colors.blue.400);
  --color-info-foreground: theme(colors.neutral.50);
}

/* 
 * Dark mode overrides
 * These override the theme variables when .dark class is applied
 */
.dark {
  --color-background: theme(colors.neutral.950);
  --color-foreground: theme(colors.neutral.50);
  --color-card: theme(colors.neutral.900);
  --color-card-foreground: theme(colors.neutral.50);
  --color-popover: theme(colors.neutral.900);
  --color-popover-foreground: theme(colors.neutral.50);

  --color-muted: theme(colors.neutral.800);
  --color-muted-foreground: theme(colors.neutral.400);

  --color-border: theme(colors.neutral.800);
  --color-input: theme(colors.neutral.800);
  --color-ring: theme(colors.green.500);

  --color-primary: theme(colors.green.500);
  --color-primary-muted: theme(colors.green.600);
  --color-primary-foreground: theme(colors.neutral.950);

  --color-secondary: theme(colors.neutral.800);
  --color-secondary-muted: theme(colors.neutral.700);
  --color-secondary-foreground: theme(colors.neutral.50);

  --color-accent: theme(colors.neutral.800);
  --color-accent-muted: theme(colors.neutral.700);
  --color-accent-foreground: theme(colors.neutral.50);

  --color-destructive: theme(colors.red.600);
  --color-destructive-muted: theme(colors.red.500);
  --color-destructive-foreground: theme(colors.neutral.50);

  --color-placeholder: theme(colors.neutral.500);
  --color-success: theme(colors.green.500);
  --color-success-muted: theme(colors.green.600);
  --color-success-foreground: theme(colors.neutral.950);
  --color-warning: theme(colors.amber.500);
  --color-warning-muted: theme(colors.amber.600);
  --color-warning-foreground: theme(colors.neutral.50);
  --color-info: theme(colors.blue.500);
  --color-info-muted: theme(colors.blue.600);
  --color-info-foreground: theme(colors.neutral.50);
}

/* 
 * System preference dark mode
 * Applies dark theme when user prefers dark mode but hasn't explicitly set a theme
 */
@media (prefers-color-scheme: dark) {
  :root:not([class]) {
    --color-background: theme(colors.neutral.950);
    --color-foreground: theme(colors.neutral.50);
    --color-card: theme(colors.neutral.900);
    --color-card-foreground: theme(colors.neutral.50);
    --color-popover: theme(colors.neutral.900);
    --color-popover-foreground: theme(colors.neutral.50);

    --color-muted: theme(colors.neutral.800);
    --color-muted-foreground: theme(colors.neutral.400);

    --color-border: theme(colors.neutral.800);
    --color-input: theme(colors.neutral.800);
    --color-ring: theme(colors.green.500);

    --color-primary: theme(colors.green.500);
    --color-primary-muted: theme(colors.green.600);
    --color-primary-foreground: theme(colors.neutral.950);

    --color-secondary: theme(colors.neutral.800);
    --color-secondary-muted: theme(colors.neutral.700);
    --color-secondary-foreground: theme(colors.neutral.50);

    --color-accent: theme(colors.neutral.800);
    --color-accent-muted: theme(colors.neutral.700);
    --color-accent-foreground: theme(colors.neutral.50);

    --color-destructive: theme(colors.red.600);
    --color-destructive-muted: theme(colors.red.500);
    --color-destructive-foreground: theme(colors.neutral.50);

    --color-placeholder: theme(colors.neutral.500);
    --color-success: theme(colors.green.500);
    --color-success-muted: theme(colors.green.600);
    --color-success-foreground: theme(colors.neutral.950);
    --color-warning: theme(colors.amber.500);
    --color-warning-muted: theme(colors.amber.600);
    --color-warning-foreground: theme(colors.neutral.50);
    --color-info: theme(colors.blue.500);
    --color-info-muted: theme(colors.blue.600);
    --color-info-foreground: theme(colors.neutral.50);
  }
}

/* Base styling for the document */
body {
  background-color: var(--color-background);
  color: var(--color-foreground);
  font-family: "Geist", ui-sans-serif, system-ui, sans-serif;
  line-height: 1.6;
}