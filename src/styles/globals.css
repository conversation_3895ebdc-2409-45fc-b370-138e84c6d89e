@import "tailwindcss";

/*
 * Adobe Spectrum-inspired Design Token System
 * Following semantic naming conventions and hierarchical structure
 */
@theme {
  /* ===== GLOBAL TOKENS ===== */

  /* Color Palette - Base colors that don't change between themes */
  --color-gray-25: #fcfcfd;
  --color-gray-50: #f9fafb;
  --color-gray-75: #f4f6f8;
  --color-gray-100: #eef2f6;
  --color-gray-200: #e0e6ed;
  --color-gray-300: #c7d0dd;
  --color-gray-400: #9ca9bc;
  --color-gray-500: #6c7b8a;
  --color-gray-600: #545d69;
  --color-gray-700: #3e434a;
  --color-gray-800: #2a2d33;
  --color-gray-900: #1a1d23;
  --color-gray-950: #0f1114;

  --color-blue-100: #e6f4ff;
  --color-blue-200: #bae0ff;
  --color-blue-300: #7cc7ff;
  --color-blue-400: #378ef0;
  --color-blue-500: #0f62fe;
  --color-blue-600: #0043ce;
  --color-blue-700: #002d9c;
  --color-blue-800: #001d6c;
  --color-blue-900: #001141;

  --color-green-100: #e6f6ea;
  --color-green-200: #b3e6c7;
  --color-green-300: #6fdc8c;
  --color-green-400: #36b37e;
  --color-green-500: #00875a;
  --color-green-600: #006644;
  --color-green-700: #004b32;
  --color-green-800: #003317;
  --color-green-900: #001e0a;

  --color-red-100: #ffebe6;
  --color-red-200: #ffbdad;
  --color-red-300: #ff8f73;
  --color-red-400: #ff5630;
  --color-red-500: #de350b;
  --color-red-600: #bf2600;
  --color-red-700: #9f1a00;
  --color-red-800: #7a0e00;
  --color-red-900: #5d0a00;

  --color-orange-100: #fff4e6;
  --color-orange-200: #ffd79e;
  --color-orange-300: #ffab00;
  --color-orange-400: #ff8b00;
  --color-orange-500: #ff7300;
  --color-orange-600: #de5a00;
  --color-orange-700: #bf4100;
  --color-orange-800: #9f2a00;
  --color-orange-900: #7a1a00;

  /* ===== SEMANTIC TOKENS - Light Theme ===== */

  /* Background Colors */
  --color-background-base: var(--color-gray-25);
  --color-background-layer-1: var(--color-gray-50);
  --color-background-layer-2: var(--color-gray-75);

  /* Surface Colors */
  --color-surface-base: #ffffff;
  --color-surface-raised: #ffffff;
  --color-surface-overlay: #ffffff;

  /* Text Colors */
  --color-text-primary: var(--color-gray-900);
  --color-text-secondary: var(--color-gray-700);
  --color-text-tertiary: var(--color-gray-500);
  --color-text-disabled: var(--color-gray-400);
  --color-text-inverse: #ffffff;

  /* Border Colors */
  --color-border-base: var(--color-gray-200);
  --color-border-strong: var(--color-gray-300);
  --color-border-subtle: var(--color-gray-100);

  /* Interactive Colors */
  --color-accent-content-default: var(--color-blue-600);
  --color-accent-content-hover: var(--color-blue-700);
  --color-accent-content-down: var(--color-blue-800);
  --color-accent-content-focus: var(--color-blue-600);

  --color-accent-background-default: var(--color-blue-600);
  --color-accent-background-hover: var(--color-blue-700);
  --color-accent-background-down: var(--color-blue-800);

  --color-accent-border-default: var(--color-blue-600);
  --color-accent-border-hover: var(--color-blue-700);
  --color-accent-border-focus: var(--color-blue-600);

  /* Semantic Status Colors */
  --color-positive-content-default: var(--color-green-600);
  --color-positive-background-default: var(--color-green-100);
  --color-positive-border-default: var(--color-green-300);

  --color-negative-content-default: var(--color-red-600);
  --color-negative-background-default: var(--color-red-100);
  --color-negative-border-default: var(--color-red-300);

  --color-notice-content-default: var(--color-orange-600);
  --color-notice-background-default: var(--color-orange-100);
  --color-notice-border-default: var(--color-orange-300);

  --color-informative-content-default: var(--color-blue-600);
  --color-informative-background-default: var(--color-blue-100);
  --color-informative-border-default: var(--color-blue-300);

  /* Legacy mappings for backward compatibility */
  --color-background: var(--color-background-base);
  --color-foreground: var(--color-text-primary);
  --color-card: var(--color-surface-base);
  --color-card-foreground: var(--color-text-primary);
  --color-popover: var(--color-surface-overlay);
  --color-popover-foreground: var(--color-text-primary);
  --color-muted: var(--color-background-layer-1);
  --color-muted-foreground: var(--color-text-secondary);
  --color-border: var(--color-border-base);
  --color-input: var(--color-border-base);
  --color-ring: var(--color-accent-border-focus);
  --color-primary: var(--color-accent-background-default);
  --color-primary-foreground: var(--color-text-inverse);
  --color-secondary: var(--color-background-layer-1);
  --color-secondary-foreground: var(--color-text-primary);
  --color-accent: var(--color-background-layer-1);
  --color-accent-foreground: var(--color-text-primary);
  --color-destructive: var(--color-negative-content-default);
  --color-destructive-foreground: var(--color-text-inverse);
  --color-success: var(--color-positive-content-default);
  --color-success-foreground: var(--color-text-inverse);
  --color-warning: var(--color-notice-content-default);
  --color-warning-foreground: var(--color-text-inverse);
  --color-info: var(--color-informative-content-default);
  --color-info-foreground: var(--color-text-inverse);

  /* ===== SPACING TOKENS ===== */
  /* Following 4px base unit system */
  --spacing-25: 0.125rem;
  /* 2px */
  --spacing-50: 0.25rem;
  /* 4px */
  --spacing-75: 0.375rem;
  /* 6px */
  --spacing-100: 0.5rem;
  /* 8px */
  --spacing-125: 0.625rem;
  /* 10px */
  --spacing-150: 0.75rem;
  /* 12px */
  --spacing-200: 1rem;
  /* 16px */
  --spacing-250: 1.25rem;
  /* 20px */
  --spacing-300: 1.5rem;
  /* 24px */
  --spacing-400: 2rem;
  /* 32px */
  --spacing-500: 2.5rem;
  /* 40px */
  --spacing-600: 3rem;
  /* 48px */
  --spacing-700: 3.5rem;
  /* 56px */
  --spacing-800: 4rem;
  /* 64px */
  --spacing-900: 4.5rem;
  /* 72px */
  --spacing-1000: 5rem;
  /* 80px */

  /* ===== SIZING TOKENS ===== */
  --size-25: 0.125rem;
  /* 2px */
  --size-50: 0.25rem;
  /* 4px */
  --size-75: 0.375rem;
  /* 6px */
  --size-100: 0.5rem;
  /* 8px */
  --size-125: 0.625rem;
  /* 10px */
  --size-150: 0.75rem;
  /* 12px */
  --size-200: 1rem;
  /* 16px */
  --size-250: 1.25rem;
  /* 20px */
  --size-300: 1.5rem;
  /* 24px */
  --size-400: 2rem;
  /* 32px */
  --size-500: 2.5rem;
  /* 40px */
  --size-600: 3rem;
  /* 48px */
  --size-700: 3.5rem;
  /* 56px */
  --size-800: 4rem;
  /* 64px */
  --size-900: 4.5rem;
  /* 72px */
  --size-1000: 5rem;
  /* 80px */

  /* Component-specific sizes */
  --size-button-height-small: 2rem;
  /* 32px */
  --size-button-height-medium: 2.5rem;
  /* 40px */
  --size-button-height-large: 3rem;
  /* 48px */

  --size-input-height-small: 2rem;
  /* 32px */
  --size-input-height-medium: 2.5rem;
  /* 40px */
  --size-input-height-large: 3rem;
  /* 48px */

  /* ===== BORDER RADIUS TOKENS ===== */
  --border-radius-none: 0;
  --border-radius-small: 0.25rem;
  /* 4px */
  --border-radius-medium: 0.375rem;
  /* 6px */
  --border-radius-large: 0.5rem;
  /* 8px */
  --border-radius-extra-large: 0.75rem;
  /* 12px */
  --border-radius-full: 9999px;

  /* ===== BORDER WIDTH TOKENS ===== */
  --border-width-none: 0;
  --border-width-thin: 1px;
  --border-width-thick: 2px;
  --border-width-thicker: 4px;

  /* ===== SHADOW TOKENS ===== */
  --shadow-none: none;
  --shadow-small: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-medium: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-large: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-extra-large: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-inner: inset 0 2px 4px 0 rgb(0 0 0 / 0.05);

  /* ===== TYPOGRAPHY TOKENS ===== */
  --font-size-50: 0.75rem;
  /* 12px */
  --font-size-75: 0.875rem;
  /* 14px */
  --font-size-100: 1rem;
  /* 16px */
  --font-size-200: 1.125rem;
  /* 18px */
  --font-size-300: 1.25rem;
  /* 20px */
  --font-size-400: 1.5rem;
  /* 24px */
  --font-size-500: 1.875rem;
  /* 30px */
  --font-size-600: 2.25rem;
  /* 36px */
  --font-size-700: 3rem;
  /* 48px */
  --font-size-800: 3.75rem;
  /* 60px */
  --font-size-900: 4.5rem;
  /* 72px */

  --line-height-100: 1;
  --line-height-125: 1.25;
  --line-height-150: 1.5;
  --line-height-175: 1.75;
  --line-height-200: 2;

  --font-weight-light: 300;
  --font-weight-regular: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;
}

/* ===== DARK THEME OVERRIDES ===== */
.dark {
  /* Background Colors */
  --color-background-base: var(--color-gray-950);
  --color-background-layer-1: var(--color-gray-900);
  --color-background-layer-2: var(--color-gray-800);

  /* Surface Colors */
  --color-surface-base: var(--color-gray-900);
  --color-surface-raised: var(--color-gray-800);
  --color-surface-overlay: var(--color-gray-800);

  /* Text Colors */
  --color-text-primary: var(--color-gray-50);
  --color-text-secondary: var(--color-gray-300);
  --color-text-tertiary: var(--color-gray-400);
  --color-text-disabled: var(--color-gray-500);
  --color-text-inverse: var(--color-gray-900);

  /* Border Colors */
  --color-border-base: var(--color-gray-700);
  --color-border-strong: var(--color-gray-600);
  --color-border-subtle: var(--color-gray-800);

  /* Interactive Colors */
  --color-accent-content-default: var(--color-blue-400);
  --color-accent-content-hover: var(--color-blue-300);
  --color-accent-content-down: var(--color-blue-500);
  --color-accent-content-focus: var(--color-blue-400);

  --color-accent-background-default: var(--color-blue-600);
  --color-accent-background-hover: var(--color-blue-500);
  --color-accent-background-down: var(--color-blue-700);

  --color-accent-border-default: var(--color-blue-500);
  --color-accent-border-hover: var(--color-blue-400);
  --color-accent-border-focus: var(--color-blue-400);

  /* Semantic Status Colors */
  --color-positive-content-default: var(--color-green-400);
  --color-positive-background-default: var(--color-green-900);
  --color-positive-border-default: var(--color-green-700);

  --color-negative-content-default: var(--color-red-400);
  --color-negative-background-default: var(--color-red-900);
  --color-negative-border-default: var(--color-red-700);

  --color-notice-content-default: var(--color-orange-400);
  --color-notice-background-default: var(--color-orange-900);
  --color-notice-border-default: var(--color-orange-700);

  --color-informative-content-default: var(--color-blue-400);
  --color-informative-background-default: var(--color-blue-900);
  --color-informative-border-default: var(--color-blue-700);
}

/* ===== SYSTEM PREFERENCE DARK MODE ===== */
@media (prefers-color-scheme: dark) {
  :root:not([class]) {
    /* Background Colors */
    --color-background-base: var(--color-gray-950);
    --color-background-layer-1: var(--color-gray-900);
    --color-background-layer-2: var(--color-gray-800);

    /* Surface Colors */
    --color-surface-base: var(--color-gray-900);
    --color-surface-raised: var(--color-gray-800);
    --color-surface-overlay: var(--color-gray-800);

    /* Text Colors */
    --color-text-primary: var(--color-gray-50);
    --color-text-secondary: var(--color-gray-300);
    --color-text-tertiary: var(--color-gray-400);
    --color-text-disabled: var(--color-gray-500);
    --color-text-inverse: var(--color-gray-900);

    /* Border Colors */
    --color-border-base: var(--color-gray-700);
    --color-border-strong: var(--color-gray-600);
    --color-border-subtle: var(--color-gray-800);

    /* Interactive Colors */
    --color-accent-content-default: var(--color-blue-400);
    --color-accent-content-hover: var(--color-blue-300);
    --color-accent-content-down: var(--color-blue-500);
    --color-accent-content-focus: var(--color-blue-400);

    --color-accent-background-default: var(--color-blue-600);
    --color-accent-background-hover: var(--color-blue-500);
    --color-accent-background-down: var(--color-blue-700);

    --color-accent-border-default: var(--color-blue-500);
    --color-accent-border-hover: var(--color-blue-400);
    --color-accent-border-focus: var(--color-blue-400);

    /* Semantic Status Colors */
    --color-positive-content-default: var(--color-green-400);
    --color-positive-background-default: var(--color-green-900);
    --color-positive-border-default: var(--color-green-700);

    --color-negative-content-default: var(--color-red-400);
    --color-negative-background-default: var(--color-red-900);
    --color-negative-border-default: var(--color-red-700);

    --color-notice-content-default: var(--color-orange-400);
    --color-notice-background-default: var(--color-orange-900);
    --color-notice-border-default: var(--color-orange-700);

    --color-informative-content-default: var(--color-blue-400);
    --color-informative-background-default: var(--color-blue-900);
    --color-informative-border-default: var(--color-blue-700);
  }
}

/* Base styling for the document */
body {
  background-color: var(--color-background);
  color: var(--color-foreground);
  font-family: "Geist", ui-sans-serif, system-ui, sans-serif;
  line-height: 1.6;
}