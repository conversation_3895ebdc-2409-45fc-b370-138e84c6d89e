@import './design-tokens.css';
@import "tailwindcss";

/*
 * Clean Design Token System
 * All defaults are defined in design-tokens.css
 * This file only contains the base color palette and semantic mappings
 */

/* Define CSS custom properties first */
:root {
  /* ===== BASE COLOR PALETTE ===== */
  /* OKCH colors for better perceptual uniformity */

  /* Gray Scale */
  --gray-25: oklch(99% 0.002 247);
  --gray-50: oklch(97% 0.003 247);
  --gray-75: oklch(95% 0.004 247);
  --gray-100: oklch(92% 0.005 247);
  --gray-200: oklch(87% 0.007 247);
  --gray-300: oklch(78% 0.012 247);
  --gray-400: oklch(65% 0.015 247);
  --gray-500: oklch(52% 0.012 247);
  --gray-600: oklch(42% 0.010 247);
  --gray-700: oklch(32% 0.008 247);
  --gray-800: oklch(22% 0.006 247);
  --gray-900: oklch(15% 0.004 247);
  --gray-950: oklch(10% 0.002 247);

  /* Blue Scale */
  --blue-100: oklch(95% 0.025 250);
  --blue-200: oklch(87% 0.045 250);
  --blue-300: oklch(75% 0.075 250);
  --blue-400: oklch(62% 0.125 250);
  --blue-500: oklch(55% 0.155 250);
  --blue-600: oklch(45% 0.135 250);
  --blue-700: oklch(35% 0.115 250);
  --blue-800: oklch(25% 0.095 250);
  --blue-900: oklch(15% 0.075 250);

  /* Green Scale */
  --green-100: oklch(94% 0.035 145);
  --green-200: oklch(85% 0.065 145);
  --green-300: oklch(75% 0.095 145);
  --green-400: oklch(65% 0.115 145);
  --green-500: oklch(55% 0.125 145);
  --green-600: oklch(45% 0.105 145);
  --green-700: oklch(35% 0.085 145);
  --green-800: oklch(25% 0.065 145);
  --green-900: oklch(15% 0.045 145);

  /* Red Scale */
  --red-100: oklch(94% 0.025 25);
  --red-200: oklch(82% 0.055 25);
  --red-300: oklch(70% 0.085 25);
  --red-400: oklch(62% 0.125 25);
  --red-500: oklch(52% 0.145 25);
  --red-600: oklch(42% 0.125 25);
  --red-700: oklch(32% 0.105 25);
  --red-800: oklch(22% 0.085 25);
  --red-900: oklch(15% 0.065 25);

  /* Orange Scale */
  --orange-100: oklch(96% 0.025 65);
  --orange-200: oklch(87% 0.055 65);
  --orange-300: oklch(75% 0.095 65);
  --orange-400: oklch(68% 0.125 65);
  --orange-500: oklch(62% 0.145 65);
  --orange-600: oklch(52% 0.125 65);
  --orange-700: oklch(42% 0.105 65);
  --orange-800: oklch(32% 0.085 65);
  --orange-900: oklch(22% 0.065 65);

  /* ===== SEMANTIC TOKENS - Light Theme ===== */
  /* Using design token defaults from design-tokens.css */

  /* Background Colors */
  --background-base: var(--gray-25);
  --background-layer-1: var(--gray-50);
  --background-layer-2: var(--gray-75);

  /* Surface Colors */
  --surface-base: oklch(100% 0 0);
  --surface-raised: oklch(100% 0 0);
  --surface-overlay: oklch(100% 0 0);

  /* Text Colors */
  --text-primary: var(--gray-900);
  --text-secondary: var(--gray-700);
  --text-tertiary: var(--gray-500);
  --text-disabled: var(--gray-400);
  --text-inverse: oklch(100% 0 0);

  /* Border Colors */
  --border-base: var(--gray-200);
  --border-strong: var(--gray-300);
  --border-subtle: var(--gray-100);

  /* Interactive Colors - Green Primary */
  --accent-content-default: var(--green-600);
  --accent-content-hover: var(--green-700);
  --accent-content-down: var(--green-800);
  --accent-content-focus: var(--green-600);

  --accent-background-default: var(--green-600);
  --accent-background-hover: var(--green-700);
  --accent-background-down: var(--green-800);

  --accent-border-default: var(--green-600);
  --accent-border-hover: var(--green-700);
  --accent-border-focus: var(--green-600);

  /* Semantic Status Colors */
  --positive-content: var(--green-600);
  --positive-background: var(--green-100);
  --positive-border: var(--green-300);

  --negative-content: var(--red-600);
  --negative-background: var(--red-100);
  --negative-border: var(--red-300);

  --notice-content: var(--orange-600);
  --notice-background: var(--orange-100);
  --notice-border: var(--orange-300);

  --informative-content: var(--blue-600);
  --informative-background: var(--blue-100);
  --informative-border: var(--blue-300);

  /* ===== DESIGN TOKEN MAPPINGS ===== */
  /* Import all design token values for Tailwind CSS */

  /* Spacing tokens from design-tokens.css */
  --spacing-component-xs: var(--spacing-scale-xs, 0.125rem);
  --spacing-component-sm: var(--spacing-scale-sm, 0.25rem);
  --spacing-component-base: var(--spacing-scale-base, 0.5rem);
  --spacing-component-md: var(--spacing-scale-md, 0.75rem);
  --spacing-component-lg: var(--spacing-scale-lg, 1rem);
  --spacing-component-xl: var(--spacing-scale-xl, 1.5rem);
  --spacing-component-2xl: var(--spacing-scale-2xl, 2rem);

  /* Typography tokens from design-tokens.css */
  --text-component-xs: var(--text-scale-xs, 0.75rem);
  --text-component-sm: var(--text-scale-sm, 0.875rem);
  --text-component-base: var(--text-scale-base, 1rem);
  --text-component-lg: var(--text-scale-lg, 1.125rem);
  --text-component-xl: var(--text-scale-xl, 1.25rem);
  --text-component-2xl: var(--text-scale-2xl, 1.5rem);
  --text-component-3xl: var(--text-scale-3xl, 1.875rem);
  --text-component-4xl: var(--text-scale-4xl, 2.25rem);

  /* Border radius tokens from design-tokens.css */
  --radius-default: var(--radius-scale-medium, 1rem);
  --radius-button: var(--radius-scale-large, 1.5rem);
  --radius-card: var(--radius-scale-xl, 2rem);
  --radius-input: var(--radius-scale-medium, 1rem);
  --radius-badge: var(--radius-scale-large, 1.5rem);
  --radius-progress: var(--radius-scale-small, 0.75rem);
  --radius-scale-small: 0.75rem;
  --radius-scale-medium: 1rem;
  --radius-scale-large: 1.5rem;
  --radius-scale-xl: 2rem;

  /* Shadow tokens from design-tokens.css */
  --shadow-component-subtle: var(--shadow-scale-subtle, 0 1px 2px 0 rgb(0 0 0 / 0.05));
  --shadow-component-small: var(--shadow-scale-small, 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1));
  --shadow-component-medium: var(--shadow-scale-medium, 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1));
  --shadow-component-large: var(--shadow-scale-large, 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1));
}

/* Now define the Tailwind theme */
@theme {
  /* Use the CSS custom properties defined above */
  --color-background-base: var(--background-base);
  --color-background-layer-1: var(--background-layer-1);
  --color-background-layer-2: var(--background-layer-2);

  --color-surface-base: var(--surface-base);
  --color-surface-raised: var(--surface-raised);
  --color-surface-overlay: var(--surface-overlay);

  --color-text-primary: var(--text-primary);
  --color-text-secondary: var(--text-secondary);
  --color-text-tertiary: var(--text-tertiary);
  --color-text-disabled: var(--text-disabled);
  --color-text-inverse: var(--text-inverse);

  --color-border-base: var(--border-base);
  --color-border-strong: var(--border-strong);
  --color-border-subtle: var(--border-subtle);

  --color-accent-content-default: var(--accent-content-default);
  --color-accent-content-hover: var(--accent-content-hover);
  --color-accent-content-down: var(--accent-content-down);
  --color-accent-content-focus: var(--accent-content-focus);
  --color-accent-background-default: var(--accent-background-default);
  --color-accent-background-hover: var(--accent-background-hover);
  --color-accent-background-down: var(--accent-background-down);
  --color-accent-border-default: var(--accent-border-default);
  --color-accent-border-hover: var(--accent-border-hover);
  --color-accent-border-focus: var(--accent-border-focus);

  --color-positive-content: var(--positive-content);
  --color-positive-background: var(--positive-background);
  --color-positive-border: var(--positive-border);
  --color-negative-content: var(--negative-content);
  --color-negative-background: var(--negative-background);
  --color-negative-border: var(--negative-border);
  --color-notice-content: var(--notice-content);
  --color-notice-background: var(--notice-background);
  --color-notice-border: var(--notice-border);
  --color-informative-content: var(--informative-content);
  --color-informative-background: var(--informative-background);
  --color-informative-border: var(--informative-border);

  /* Spacing */
  --spacing-25: var(--spacing-component-xs);
  --spacing-50: var(--spacing-component-sm);
  --spacing-75: var(--spacing-component-sm);
  --spacing-100: var(--spacing-component-base);
  --spacing-125: var(--spacing-component-base);
  --spacing-150: var(--spacing-component-md);
  --spacing-200: var(--spacing-component-lg);
  --spacing-250: var(--spacing-component-lg);
  --spacing-300: var(--spacing-component-xl);
  --spacing-400: var(--spacing-component-2xl);
  --spacing-500: var(--spacing-component-2xl);
  --spacing-600: var(--spacing-component-2xl);
  --spacing-700: var(--spacing-component-2xl);
  --spacing-800: var(--spacing-component-2xl);
  --spacing-900: var(--spacing-component-2xl);
  --spacing-1000: var(--spacing-component-2xl);

  /* Typography */
  --font-size-50: var(--text-component-xs);
  --font-size-75: var(--text-component-sm);
  --font-size-100: var(--text-component-base);
  --font-size-200: var(--text-component-lg);
  --font-size-300: var(--text-component-xl);
  --font-size-400: var(--text-component-2xl);
  --font-size-500: var(--text-component-3xl);
  --font-size-600: var(--text-component-4xl);
  --font-size-700: var(--text-component-4xl);
  --font-size-800: var(--text-component-4xl);
  --font-size-900: var(--text-component-4xl);

  /* Border radius */
  --border-radius-small: var(--radius-scale-small);
  --border-radius-medium: var(--radius-scale-medium);
  --border-radius-large: var(--radius-scale-large);
  --border-radius-extra-large: var(--radius-scale-xl);
  --border-radius-card: var(--radius-card);
  --border-radius-button: var(--radius-button);
  --border-radius-input: var(--radius-input);
  --border-radius-badge: var(--radius-badge);
  --border-radius-progress: var(--radius-progress);

  /* Shadows */
  --box-shadow-small: var(--shadow-component-small);
  --box-shadow-medium: var(--shadow-component-medium);
  --box-shadow-large: var(--shadow-component-large);
}

/* Tailwind theme configuration is now in tailwind.config.ts */

/* ===== DARK THEME OVERRIDES ===== */
.dark {
  /* Background Colors */
  --background-base: var(--gray-950);
  --background-layer-1: var(--gray-900);
  --background-layer-2: var(--gray-800);

  /* Surface Colors */
  --surface-base: var(--gray-900);
  --surface-raised: var(--gray-800);
  --surface-overlay: var(--gray-800);

  /* Text Colors */
  --text-primary: var(--gray-50);
  --text-secondary: var(--gray-300);
  --text-tertiary: var(--gray-400);
  --text-disabled: var(--gray-500);
  --text-inverse: var(--gray-900);

  /* Border Colors */
  --border-base: var(--gray-700);
  --border-strong: var(--gray-600);
  --border-subtle: var(--gray-800);

  /* Interactive Colors - Green Primary for Dark Mode */
  --accent-content-default: var(--green-400);
  --accent-content-hover: var(--green-300);
  --accent-content-down: var(--green-500);
  --accent-content-focus: var(--green-400);

  --accent-background-default: var(--green-600);
  --accent-background-hover: var(--green-500);
  --accent-background-down: var(--green-700);

  --accent-border-default: var(--green-500);
  --accent-border-hover: var(--green-400);
  --accent-border-focus: var(--green-400);

  /* Semantic Status Colors */
  --positive-content: var(--green-400);
  --positive-background: var(--green-900);
  --positive-border: var(--green-700);

  --negative-content: var(--red-400);
  --negative-background: var(--red-900);
  --negative-border: var(--red-700);

  --notice-content: var(--orange-400);
  --notice-background: var(--orange-900);
  --notice-border: var(--orange-700);

  --informative-content: var(--blue-400);
  --informative-background: var(--blue-900);
  --informative-border: var(--blue-700);

  /* Design tokens remain the same in dark mode */
  --spacing-component-xs: var(--spacing-scale-xs, 0.125rem);
  --spacing-component-sm: var(--spacing-scale-sm, 0.25rem);
  --spacing-component-base: var(--spacing-scale-base, 0.5rem);
  --spacing-component-md: var(--spacing-scale-md, 0.75rem);
  --spacing-component-lg: var(--spacing-scale-lg, 1rem);
  --spacing-component-xl: var(--spacing-scale-xl, 1.5rem);
  --spacing-component-2xl: var(--spacing-scale-2xl, 2rem);

  --text-component-xs: var(--text-scale-xs, 0.75rem);
  --text-component-sm: var(--text-scale-sm, 0.875rem);
  --text-component-base: var(--text-scale-base, 1rem);
  --text-component-lg: var(--text-scale-lg, 1.125rem);
  --text-component-xl: var(--text-scale-xl, 1.25rem);
  --text-component-2xl: var(--text-scale-2xl, 1.5rem);
  --text-component-3xl: var(--text-scale-3xl, 1.875rem);
  --text-component-4xl: var(--text-scale-4xl, 2.25rem);

  --radius-default: var(--radius-scale-medium, 1rem);
  --radius-button: var(--radius-scale-large, 1.5rem);
  --radius-card: var(--radius-scale-xl, 2rem);
  --radius-input: var(--radius-scale-medium, 1rem);
  --radius-badge: var(--radius-scale-large, 1.5rem);
  --radius-progress: var(--radius-scale-small, 0.75rem);
  --radius-scale-small: 0.75rem;
  --radius-scale-medium: 1rem;
  --radius-scale-large: 1.5rem;
  --radius-scale-xl: 2rem;

  --shadow-component-subtle: var(--shadow-scale-subtle, 0 1px 2px 0 rgb(0 0 0 / 0.05));
  --shadow-component-small: var(--shadow-scale-small, 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1));
  --shadow-component-medium: var(--shadow-scale-medium, 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1));
  --shadow-component-large: var(--shadow-scale-large, 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1));
}

/* ===== SYSTEM PREFERENCE DARK MODE ===== */
@media (prefers-color-scheme: dark) {
  :root:not([class]) {
    /* Background Colors */
    --background-base: var(--gray-950);
    --background-layer-1: var(--gray-900);
    --background-layer-2: var(--gray-800);

    /* Surface Colors */
    --surface-base: var(--gray-900);
    --surface-raised: var(--gray-800);
    --surface-overlay: var(--gray-800);

    /* Text Colors */
    --text-primary: var(--gray-50);
    --text-secondary: var(--gray-300);
    --text-tertiary: var(--gray-400);
    --text-disabled: var(--gray-500);
    --text-inverse: var(--gray-900);

    /* Border Colors */
    --border-base: var(--gray-700);
    --border-strong: var(--gray-600);
    --border-subtle: var(--gray-800);

    /* Interactive Colors - Green Primary for System Dark Mode */
    --accent-content-default: var(--green-400);
    --accent-content-hover: var(--green-300);
    --accent-content-down: var(--green-500);
    --accent-content-focus: var(--green-400);

    --accent-background-default: var(--green-600);
    --accent-background-hover: var(--green-500);
    --accent-background-down: var(--green-700);

    --accent-border-default: var(--green-500);
    --accent-border-hover: var(--green-400);
    --accent-border-focus: var(--green-400);

    /* Semantic Status Colors */
    --positive-content: var(--green-400);
    --positive-background: var(--green-900);
    --positive-border: var(--green-700);

    --negative-content: var(--red-400);
    --negative-background: var(--red-900);
    --negative-border: var(--red-700);

    --notice-content: var(--orange-400);
    --notice-background: var(--orange-900);
    --notice-border: var(--orange-700);

    --informative-content: var(--blue-400);
    --informative-background: var(--blue-900);
    --informative-border: var(--blue-700);

    /* Design tokens remain the same in system dark mode */
    --spacing-component-xs: var(--spacing-scale-xs, 0.125rem);
    --spacing-component-sm: var(--spacing-scale-sm, 0.25rem);
    --spacing-component-base: var(--spacing-scale-base, 0.5rem);
    --spacing-component-md: var(--spacing-scale-md, 0.75rem);
    --spacing-component-lg: var(--spacing-scale-lg, 1rem);
    --spacing-component-xl: var(--spacing-scale-xl, 1.5rem);
    --spacing-component-2xl: var(--spacing-scale-2xl, 2rem);

    --text-component-xs: var(--text-scale-xs, 0.75rem);
    --text-component-sm: var(--text-scale-sm, 0.875rem);
    --text-component-base: var(--text-scale-base, 1rem);
    --text-component-lg: var(--text-scale-lg, 1.125rem);
    --text-component-xl: var(--text-scale-xl, 1.25rem);
    --text-component-2xl: var(--text-scale-2xl, 1.5rem);
    --text-component-3xl: var(--text-scale-3xl, 1.875rem);
    --text-component-4xl: var(--text-scale-4xl, 2.25rem);

    --radius-default: var(--radius-scale-medium, 1rem);
    --radius-button: var(--radius-scale-large, 1.5rem);
    --radius-card: var(--radius-scale-xl, 2rem);
    --radius-input: var(--radius-scale-medium, 1rem);
    --radius-badge: var(--radius-scale-large, 1.5rem);
    --radius-progress: var(--radius-scale-small, 0.75rem);
    --radius-scale-small: 0.75rem;
    --radius-scale-medium: 1rem;
    --radius-scale-large: 1.5rem;
    --radius-scale-xl: 2rem;

    --shadow-component-subtle: var(--shadow-scale-subtle, 0 1px 2px 0 rgb(0 0 0 / 0.05));
    --shadow-component-small: var(--shadow-scale-small, 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1));
    --shadow-component-medium: var(--shadow-scale-medium, 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1));
    --shadow-component-large: var(--shadow-scale-large, 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1));
  }
}

/* Base styling for the document */
body {
  background-color: var(--background-base);
  color: var(--text-primary);
  font-family: "Geist", ui-sans-serif, system-ui, sans-serif;
  line-height: 1.6;
}

/* Debug: Test if CSS custom properties are working */
.debug-radius {
  border-radius: var(--radius-card) !important;
  background: red !important;
  padding: 1rem !important;
  margin: 1rem !important;
}

/* Force rounded corners for testing */
.rounded-card {
  border-radius: var(--radius-card, 2rem) !important;
}

.rounded-button {
  border-radius: var(--radius-button, 1.5rem) !important;
}

.rounded-badge {
  border-radius: var(--radius-badge, 1.5rem) !important;
}