/**
 * Design Token Configuration
 * 
 * This file contains all the default design token values that can be easily
 * modified to change the entire app's appearance. Simply update the values
 * here and they will be applied throughout all components.
 */

:root {
  /* ===== DESIGN TOKEN DEFAULTS ===== */

  /* Primary Brand Color - Change this to update the entire app's primary color */
  --primary-hue: 88;
  /* Green hue - change to any value 0-360 */
  --primary-saturation: 0.5;
  /* Color intensity */
  --primary-lightness: 50%;
  /* Base lightness */

  /* Border Radius Scale - Change these to make the app more or less rounded */
  --radius-scale-small: 0.5rem;
  /* 8px - small elements */
  --radius-scale-medium: 0.75rem;
  /* 12px - default elements */
  --radius-scale-large: 1rem;
  /* 16px - buttons, badges */
  --radius-scale-xl: 1.25rem;
  /* 20px - cards, containers */

  /* Spacing Scale - Change these to adjust overall spacing */
  --spacing-scale-xs: 0.125rem;
  /* 2px */
  --spacing-scale-sm: 0.25rem;
  /* 4px */
  --spacing-scale-base: 0.5rem;
  /* 8px */
  --spacing-scale-md: 0.75rem;
  /* 12px */
  --spacing-scale-lg: 1rem;
  /* 16px */
  --spacing-scale-xl: 1.5rem;
  /* 24px */
  --spacing-scale-2xl: 2rem;
  /* 32px */

  /* Typography Scale - Change these to adjust text sizing */
  --text-scale-xs: 0.75rem;
  /* 12px */
  --text-scale-sm: 0.875rem;
  /* 14px */
  --text-scale-base: 1rem;
  /* 16px */
  --text-scale-lg: 1.125rem;
  /* 18px */
  --text-scale-xl: 1.25rem;
  /* 20px */
  --text-scale-2xl: 1.5rem;
  /* 24px */
  --text-scale-3xl: 1.875rem;
  /* 30px */
  --text-scale-4xl: 2.25rem;
  /* 36px */

  /* Shadow Intensity - Change these to adjust shadow prominence */
  --shadow-scale-subtle: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-scale-small: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-scale-medium: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-scale-large: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);

  /* ===== COMPONENT DEFAULTS ===== */
  /* These use the scale values above - modify the scale values to change everything */

  /* Default Border Radius for Components */
  --radius-default: var(--radius-scale-medium);
  --radius-button: var(--radius-scale-large);
  --radius-card: var(--radius-scale-xl);
  --radius-input: var(--radius-scale-medium);
  --radius-badge: var(--radius-scale-large);
  --radius-progress: var(--radius-scale-small);

  /* Default Spacing for Components */
  --spacing-component-xs: var(--spacing-scale-xs);
  --spacing-component-sm: var(--spacing-scale-sm);
  --spacing-component-base: var(--spacing-scale-base);
  --spacing-component-md: var(--spacing-scale-md);
  --spacing-component-lg: var(--spacing-scale-lg);
  --spacing-component-xl: var(--spacing-scale-xl);
  --spacing-component-2xl: var(--spacing-scale-2xl);

  /* Default Typography for Components */
  --text-component-xs: var(--text-scale-xs);
  --text-component-sm: var(--text-scale-sm);
  --text-component-base: var(--text-scale-base);
  --text-component-lg: var(--text-scale-lg);
  --text-component-xl: var(--text-scale-xl);
  --text-component-2xl: var(--text-scale-2xl);
  --text-component-3xl: var(--text-scale-3xl);
  --text-component-4xl: var(--text-scale-4xl);

  /* Default Shadows for Components */
  --shadow-component-subtle: var(--shadow-scale-subtle);
  --shadow-component-small: var(--shadow-scale-small);
  --shadow-component-medium: var(--shadow-scale-medium);
  --shadow-component-large: var(--shadow-scale-large);
}

/* ===== QUICK THEME VARIATIONS ===== */
/* Uncomment one of these to quickly change the app's appearance */

/* More Rounded Theme */
/*
:root {
  --radius-scale-small: 0.75rem;
  --radius-scale-medium: 1rem;
  --radius-scale-large: 1.5rem;
  --radius-scale-xl: 2rem;
}
*/

/* Less Rounded Theme */
/*
:root {
  --radius-scale-small: 0.25rem;
  --radius-scale-medium: 0.375rem;
  --radius-scale-large: 0.5rem;
  --radius-scale-xl: 0.75rem;
}
*/

/* Blue Primary Theme */
/*
:root {
  --primary-hue: 220;
  --primary-saturation: 0.155;
  --primary-lightness: 55%;
}
*/

/* Purple Primary Theme */
/*
:root {
  --primary-hue: 270;
  --primary-saturation: 0.135;
  --primary-lightness: 55%;
}
*/

/* Compact Spacing Theme */
/*
:root {
  --spacing-scale-xs: 0.0625rem;
  --spacing-scale-sm: 0.125rem;
  --spacing-scale-base: 0.25rem;
  --spacing-scale-md: 0.5rem;
  --spacing-scale-lg: 0.75rem;
  --spacing-scale-xl: 1rem;
  --spacing-scale-2xl: 1.5rem;
}
*/

/* Spacious Theme */
/*
:root {
  --spacing-scale-xs: 0.25rem;
  --spacing-scale-sm: 0.5rem;
  --spacing-scale-base: 0.75rem;
  --spacing-scale-md: 1rem;
  --spacing-scale-lg: 1.5rem;
  --spacing-scale-xl: 2rem;
  --spacing-scale-2xl: 3rem;
}
*/