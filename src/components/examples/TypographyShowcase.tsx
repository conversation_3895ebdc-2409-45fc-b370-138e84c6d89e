import { useState } from 'react';
import { Typo<PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>onte<PERSON>, Button } from '@/components/atoms';

/**
 * TypographyShowcase component demonstrating the responsive Typography component
 * with all variants, weights, colors, and responsive behavior
 */
export default function TypographyShowcase() {
	const [responsive, setResponsive] = useState(true);

	return (
		<div className='p-8 bg-background-base min-h-screen'>
			<div className='max-w-7xl mx-auto'>
				{/* Header */}
				<div className='mb-12'>
					<Typography variant='display' responsive={responsive} className='mb-4'>
						Typography System
					</Typography>
					<Typography variant='body-lg' color='secondary' responsive={responsive}>
						Explore our responsive typography component with semantic variants,
						multiple weights, and adaptive sizing.
					</Typography>
					
					<div className='mt-6 flex gap-4'>
						<Button
							variant={responsive ? 'primary' : 'outline'}
							onClick={() => setResponsive(true)}>
							Responsive
						</Button>
						<Button
							variant={!responsive ? 'primary' : 'outline'}
							onClick={() => setResponsive(false)}>
							Fixed Size
						</Button>
					</div>
				</div>

				{/* Display Variants */}
				<section className='mb-12'>
					<Typography variant='h2' responsive={responsive} className='mb-6'>
						Display Variants
					</Typography>
					<Card variant='outlined' size='large'>
						<CardContent>
							<div className='space-y-6'>
								<div>
									<Typography variant='display-lg' responsive={responsive}>
										Display Large
									</Typography>
									<Typography variant='caption' color='tertiary' responsive={responsive}>
										display-lg - Hero text for landing pages
									</Typography>
								</div>
								<div>
									<Typography variant='display' responsive={responsive}>
										Display Medium
									</Typography>
									<Typography variant='caption' color='tertiary' responsive={responsive}>
										display - Large promotional text
									</Typography>
								</div>
								<div>
									<Typography variant='display-sm' responsive={responsive}>
										Display Small
									</Typography>
									<Typography variant='caption' color='tertiary' responsive={responsive}>
										display-sm - Section headers
									</Typography>
								</div>
							</div>
						</CardContent>
					</Card>
				</section>

				{/* Heading Variants */}
				<section className='mb-12'>
					<Typography variant='h2' responsive={responsive} className='mb-6'>
						Heading Variants
					</Typography>
					<Card variant='outlined' size='large'>
						<CardContent>
							<div className='space-y-4'>
								<div>
									<Typography variant='h1' responsive={responsive}>
										Heading 1
									</Typography>
									<Typography variant='caption' color='tertiary' responsive={responsive}>
										h1 - Page titles
									</Typography>
								</div>
								<div>
									<Typography variant='h2' responsive={responsive}>
										Heading 2
									</Typography>
									<Typography variant='caption' color='tertiary' responsive={responsive}>
										h2 - Section titles
									</Typography>
								</div>
								<div>
									<Typography variant='h3' responsive={responsive}>
										Heading 3
									</Typography>
									<Typography variant='caption' color='tertiary' responsive={responsive}>
										h3 - Subsection titles
									</Typography>
								</div>
								<div>
									<Typography variant='h4' responsive={responsive}>
										Heading 4
									</Typography>
									<Typography variant='caption' color='tertiary' responsive={responsive}>
										h4 - Card titles
									</Typography>
								</div>
								<div>
									<Typography variant='h5' responsive={responsive}>
										Heading 5
									</Typography>
									<Typography variant='caption' color='tertiary' responsive={responsive}>
										h5 - Small headings
									</Typography>
								</div>
								<div>
									<Typography variant='h6' responsive={responsive}>
										Heading 6
									</Typography>
									<Typography variant='caption' color='tertiary' responsive={responsive}>
										h6 - Smallest headings
									</Typography>
								</div>
							</div>
						</CardContent>
					</Card>
				</section>

				{/* Body Text Variants */}
				<section className='mb-12'>
					<Typography variant='h2' responsive={responsive} className='mb-6'>
						Body Text Variants
					</Typography>
					<Card variant='outlined' size='large'>
						<CardContent>
							<div className='space-y-4'>
								<div>
									<Typography variant='body-lg' responsive={responsive}>
										Large body text - Perfect for introductory paragraphs and important content that needs more emphasis.
									</Typography>
									<Typography variant='caption' color='tertiary' responsive={responsive}>
										body-lg - Emphasized body text
									</Typography>
								</div>
								<div>
									<Typography variant='body' responsive={responsive}>
										Regular body text - The standard text size for most content, providing optimal readability across devices.
									</Typography>
									<Typography variant='caption' color='tertiary' responsive={responsive}>
										body - Default body text
									</Typography>
								</div>
								<div>
									<Typography variant='body-sm' responsive={responsive}>
										Small body text - Used for secondary information, captions, and less important content.
									</Typography>
									<Typography variant='caption' color='tertiary' responsive={responsive}>
										body-sm - Secondary body text
									</Typography>
								</div>
								<div>
									<Typography variant='caption' responsive={responsive}>
										Caption text - For metadata, timestamps, and fine print.
									</Typography>
									<Typography variant='caption' color='tertiary' responsive={responsive}>
										caption - Smallest readable text
									</Typography>
								</div>
								<div>
									<Typography variant='overline' responsive={responsive}>
										Overline Text
									</Typography>
									<Typography variant='caption' color='tertiary' responsive={responsive}>
										overline - Labels and categories
									</Typography>
								</div>
							</div>
						</CardContent>
					</Card>
				</section>

				{/* Font Weights */}
				<section className='mb-12'>
					<Typography variant='h2' responsive={responsive} className='mb-6'>
						Font Weights
					</Typography>
					<Card variant='outlined' size='large'>
						<CardContent>
							<div className='space-y-3'>
								<Typography variant='body' weight='light' responsive={responsive}>
									Light weight text - For subtle emphasis
								</Typography>
								<Typography variant='body' weight='regular' responsive={responsive}>
									Regular weight text - Default weight
								</Typography>
								<Typography variant='body' weight='medium' responsive={responsive}>
									Medium weight text - Slightly emphasized
								</Typography>
								<Typography variant='body' weight='semibold' responsive={responsive}>
									Semibold weight text - Strong emphasis
								</Typography>
								<Typography variant='body' weight='bold' responsive={responsive}>
									Bold weight text - Very strong emphasis
								</Typography>
								<Typography variant='body' weight='extrabold' responsive={responsive}>
									Extra bold weight text - Maximum emphasis
								</Typography>
							</div>
						</CardContent>
					</Card>
				</section>

				{/* Colors */}
				<section className='mb-12'>
					<Typography variant='h2' responsive={responsive} className='mb-6'>
						Text Colors
					</Typography>
					<div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
						{/* Neutral Colors */}
						<Card variant='outlined' size='medium'>
							<CardHeader>
								<Typography variant='h4' responsive={responsive}>
									Neutral Colors
								</Typography>
							</CardHeader>
							<CardContent>
								<div className='space-y-2'>
									<Typography variant='body' color='primary' responsive={responsive}>
										Primary text color
									</Typography>
									<Typography variant='body' color='secondary' responsive={responsive}>
										Secondary text color
									</Typography>
									<Typography variant='body' color='tertiary' responsive={responsive}>
										Tertiary text color
									</Typography>
									<Typography variant='body' color='disabled' responsive={responsive}>
										Disabled text color
									</Typography>
								</div>
							</CardContent>
						</Card>

						{/* Semantic Colors */}
						<Card variant='outlined' size='medium'>
							<CardHeader>
								<Typography variant='h4' responsive={responsive}>
									Semantic Colors
								</Typography>
							</CardHeader>
							<CardContent>
								<div className='space-y-2'>
									<Typography variant='body' color='accent' responsive={responsive}>
										Accent text color
									</Typography>
									<Typography variant='body' color='positive' responsive={responsive}>
										Positive text color
									</Typography>
									<Typography variant='body' color='negative' responsive={responsive}>
										Negative text color
									</Typography>
									<Typography variant='body' color='notice' responsive={responsive}>
										Notice text color
									</Typography>
									<Typography variant='body' color='informative' responsive={responsive}>
										Informative text color
									</Typography>
								</div>
							</CardContent>
						</Card>
					</div>
				</section>

				{/* Text Alignment */}
				<section className='mb-12'>
					<Typography variant='h2' responsive={responsive} className='mb-6'>
						Text Alignment
					</Typography>
					<Card variant='outlined' size='large'>
						<CardContent>
							<div className='space-y-4'>
								<Typography variant='body' align='left' responsive={responsive}>
									Left aligned text - Default alignment for most content
								</Typography>
								<Typography variant='body' align='center' responsive={responsive}>
									Center aligned text - For headings and special content
								</Typography>
								<Typography variant='body' align='right' responsive={responsive}>
									Right aligned text - For specific layout needs
								</Typography>
								<Typography variant='body' align='justify' responsive={responsive}>
									Justified text - For formal documents and articles where even margins are important for readability and professional appearance.
								</Typography>
							</div>
						</CardContent>
					</Card>
				</section>

				{/* Responsive Behavior */}
				<section className='mb-12'>
					<Typography variant='h2' responsive={responsive} className='mb-6'>
						Responsive Behavior
					</Typography>
					<Card variant='filled' size='large'>
						<CardContent>
							<Typography variant='body' color='secondary' responsive={responsive} className='mb-4'>
								Resize your browser window to see how responsive typography adapts to different screen sizes.
								The text scales smoothly using CSS clamp() functions.
							</Typography>
							<div className='bg-background-layer-1 p-6 rounded-large'>
								<Typography variant='h3' responsive={responsive} className='mb-2'>
									Responsive Heading
								</Typography>
								<Typography variant='body' responsive={responsive}>
									This text automatically scales between minimum and maximum sizes based on viewport width,
									ensuring optimal readability across all devices from mobile phones to large desktop screens.
								</Typography>
							</div>
						</CardContent>
					</Card>
				</section>
			</div>
		</div>
	);
}
