import { useState } from 'react';
import {
	CheckCircleIcon,
	ExclamationTriangleIcon,
	InformationCircleIcon,
	XCircleIcon,
	StarIcon,
	BellIcon,
} from '@heroicons/react/24/outline';
import { Badge } from '@/components/atoms';

/**
 * BadgeShowcase component demonstrating the new Badge component
 * with all variants, sizes, and emphasis levels
 */
export default function BadgeShowcase() {
	const [removableBadges, setRemovableBadges] = useState([
		'Design',
		'Development',
		'Marketing',
		'Sales',
	]);

	const handleRemoveBadge = (badge: string) => {
		setRemovableBadges(prev => prev.filter(b => b !== badge));
	};

	const addBadge = () => {
		const newBadge = `Tag ${removableBadges.length + 1}`;
		setRemovableBadges(prev => [...prev, newBadge]);
	};

	return (
		<div className='p-8 bg-background-base min-h-screen'>
			<div className='max-w-7xl mx-auto'>
				{/* Header */}
				<div className='mb-12'>
					<h1 className='text-700 font-bold text-text-primary mb-4'>
						Badge Component Showcase
					</h1>
					<p className='text-200 text-text-secondary max-w-3xl'>
						Explore our Badge component with semantic variants, multiple sizes,
						and emphasis levels following Adobe Spectrum design principles.
					</p>
				</div>

				{/* Variants */}
				<section className='mb-12'>
					<h2 className='text-500 font-semibold text-text-primary mb-6'>
						Badge Variants
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8'>
						{/* Neutral */}
						<div className='space-y-4'>
							<h3 className='text-300 font-medium text-text-primary'>Neutral</h3>
							<div className='flex flex-wrap gap-2'>
								<Badge variant='neutral' emphasis='subtle'>
									Default
								</Badge>
								<Badge variant='neutral' emphasis='strong'>
									Strong
								</Badge>
								<Badge variant='neutral' emphasis='outline'>
									Outline
								</Badge>
							</div>
						</div>

						{/* Accent */}
						<div className='space-y-4'>
							<h3 className='text-300 font-medium text-text-primary'>Accent</h3>
							<div className='flex flex-wrap gap-2'>
								<Badge variant='accent' emphasis='subtle'>
									Primary
								</Badge>
								<Badge variant='accent' emphasis='strong'>
									Strong
								</Badge>
								<Badge variant='accent' emphasis='outline'>
									Outline
								</Badge>
							</div>
						</div>

						{/* Positive */}
						<div className='space-y-4'>
							<h3 className='text-300 font-medium text-text-primary'>Positive</h3>
							<div className='flex flex-wrap gap-2'>
								<Badge variant='positive' emphasis='subtle'>
									Success
								</Badge>
								<Badge variant='positive' emphasis='strong'>
									Strong
								</Badge>
								<Badge variant='positive' emphasis='outline'>
									Outline
								</Badge>
							</div>
						</div>

						{/* Negative */}
						<div className='space-y-4'>
							<h3 className='text-300 font-medium text-text-primary'>Negative</h3>
							<div className='flex flex-wrap gap-2'>
								<Badge variant='negative' emphasis='subtle'>
									Error
								</Badge>
								<Badge variant='negative' emphasis='strong'>
									Strong
								</Badge>
								<Badge variant='negative' emphasis='outline'>
									Outline
								</Badge>
							</div>
						</div>

						{/* Notice */}
						<div className='space-y-4'>
							<h3 className='text-300 font-medium text-text-primary'>Notice</h3>
							<div className='flex flex-wrap gap-2'>
								<Badge variant='notice' emphasis='subtle'>
									Warning
								</Badge>
								<Badge variant='notice' emphasis='strong'>
									Strong
								</Badge>
								<Badge variant='notice' emphasis='outline'>
									Outline
								</Badge>
							</div>
						</div>

						{/* Informative */}
						<div className='space-y-4'>
							<h3 className='text-300 font-medium text-text-primary'>
								Informative
							</h3>
							<div className='flex flex-wrap gap-2'>
								<Badge variant='informative' emphasis='subtle'>
									Info
								</Badge>
								<Badge variant='informative' emphasis='strong'>
									Strong
								</Badge>
								<Badge variant='informative' emphasis='outline'>
									Outline
								</Badge>
							</div>
						</div>
					</div>
				</section>

				{/* Sizes */}
				<section className='mb-12'>
					<h2 className='text-500 font-semibold text-text-primary mb-6'>
						Badge Sizes
					</h2>
					<div className='flex flex-wrap items-center gap-4'>
						<Badge variant='accent' size='small'>
							Small Badge
						</Badge>
						<Badge variant='accent' size='medium'>
							Medium Badge
						</Badge>
						<Badge variant='accent' size='large'>
							Large Badge
						</Badge>
					</div>
				</section>

				{/* With Icons */}
				<section className='mb-12'>
					<h2 className='text-500 font-semibold text-text-primary mb-6'>
						Badges with Icons
					</h2>
					<div className='flex flex-wrap gap-4'>
						<Badge
							variant='positive'
							icon={<CheckCircleIcon />}
							emphasis='subtle'>
							Completed
						</Badge>
						<Badge
							variant='negative'
							icon={<XCircleIcon />}
							emphasis='subtle'>
							Failed
						</Badge>
						<Badge
							variant='notice'
							icon={<ExclamationTriangleIcon />}
							emphasis='subtle'>
							Warning
						</Badge>
						<Badge
							variant='informative'
							icon={<InformationCircleIcon />}
							emphasis='subtle'>
							Information
						</Badge>
						<Badge variant='accent' icon={<StarIcon />} emphasis='strong'>
							Featured
						</Badge>
						<Badge variant='neutral' icon={<BellIcon />} emphasis='outline'>
							Notification
						</Badge>
					</div>
				</section>

				{/* Removable Badges */}
				<section className='mb-12'>
					<h2 className='text-500 font-semibold text-text-primary mb-6'>
						Removable Badges
					</h2>
					<div className='space-y-4'>
						<div className='flex flex-wrap gap-2'>
							{removableBadges.map(badge => (
								<Badge
									key={badge}
									variant='neutral'
									emphasis='outline'
									removable
									onRemove={() => handleRemoveBadge(badge)}>
									{badge}
								</Badge>
							))}
						</div>
						<button
							onClick={addBadge}
							className='px-4 py-2 text-75 font-medium text-accent-content-default border border-accent-border-default rounded-medium hover:bg-accent-background-default/10 transition-colors duration-medium'>
							Add Badge
						</button>
					</div>
				</section>

				{/* Status Examples */}
				<section className='mb-12'>
					<h2 className='text-500 font-semibold text-text-primary mb-6'>
						Status Examples
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6'>
						<div className='p-4 bg-surface-base border border-border-base rounded-large'>
							<h3 className='text-200 font-medium text-text-primary mb-2'>
								Project Status
							</h3>
							<div className='space-y-2'>
								<div className='flex items-center justify-between'>
									<span className='text-75 text-text-secondary'>E-commerce</span>
									<Badge variant='informative' size='small'>
										In Progress
									</Badge>
								</div>
								<div className='flex items-center justify-between'>
									<span className='text-75 text-text-secondary'>Mobile App</span>
									<Badge variant='positive' size='small'>
										Completed
									</Badge>
								</div>
								<div className='flex items-center justify-between'>
									<span className='text-75 text-text-secondary'>Dashboard</span>
									<Badge variant='notice' size='small'>
										Planning
									</Badge>
								</div>
							</div>
						</div>

						<div className='p-4 bg-surface-base border border-border-base rounded-large'>
							<h3 className='text-200 font-medium text-text-primary mb-2'>
								User Roles
							</h3>
							<div className='space-y-2'>
								<div className='flex items-center justify-between'>
									<span className='text-75 text-text-secondary'>John Doe</span>
									<Badge variant='accent' size='small' emphasis='strong'>
										Admin
									</Badge>
								</div>
								<div className='flex items-center justify-between'>
									<span className='text-75 text-text-secondary'>Jane Smith</span>
									<Badge variant='neutral' size='small'>
										Editor
									</Badge>
								</div>
								<div className='flex items-center justify-between'>
									<span className='text-75 text-text-secondary'>Bob Wilson</span>
									<Badge variant='neutral' size='small' emphasis='outline'>
										Viewer
									</Badge>
								</div>
							</div>
						</div>

						<div className='p-4 bg-surface-base border border-border-base rounded-large'>
							<h3 className='text-200 font-medium text-text-primary mb-2'>
								System Health
							</h3>
							<div className='space-y-2'>
								<div className='flex items-center justify-between'>
									<span className='text-75 text-text-secondary'>API Server</span>
									<Badge variant='positive' size='small' icon={<CheckCircleIcon />}>
										Online
									</Badge>
								</div>
								<div className='flex items-center justify-between'>
									<span className='text-75 text-text-secondary'>Database</span>
									<Badge variant='notice' size='small' icon={<ExclamationTriangleIcon />}>
										Slow
									</Badge>
								</div>
								<div className='flex items-center justify-between'>
									<span className='text-75 text-text-secondary'>Cache</span>
									<Badge variant='negative' size='small' icon={<XCircleIcon />}>
										Offline
									</Badge>
								</div>
							</div>
						</div>

						<div className='p-4 bg-surface-base border border-border-base rounded-large'>
							<h3 className='text-200 font-medium text-text-primary mb-2'>
								Categories
							</h3>
							<div className='flex flex-wrap gap-1'>
								<Badge variant='accent' size='small' emphasis='outline'>
									React
								</Badge>
								<Badge variant='accent' size='small' emphasis='outline'>
									TypeScript
								</Badge>
								<Badge variant='accent' size='small' emphasis='outline'>
									Design
								</Badge>
								<Badge variant='accent' size='small' emphasis='outline'>
									UI/UX
								</Badge>
							</div>
						</div>
					</div>
				</section>
			</div>
		</div>
	);
}
