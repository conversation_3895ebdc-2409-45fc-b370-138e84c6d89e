import Menubar from '../organisms/Menubar';

export default function AppTemplate({
	children,
}: {
	children: React.ReactNode;
}) {
	const links = [
		{
			title: 'Home',
			href: '/',
			startIcon: null,
			endIcon: null,
			scope: 'user',
			onClick: () => {},
			className: '',
			disabled: false,
		},
		{
			title: 'Projects',
			href: '/projects',
			startIcon: null,
			endIcon: null,
			scope: 'user',
			onClick: () => {},
			className: '',
			disabled: false,
		},
	];
	return (
		<div className='flex min-h-screen flex-col bg-background-base text-text-primary'>
			<Menubar
				logo='PixiGenerator'
				links={links}
			/>
			<main className='flex-1'>{children}</main>
		</div>
	);
}
