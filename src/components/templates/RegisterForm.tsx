import { <PERSON>, Label, Fieldset, Legend } from '@headlessui/react';
import {
	Input,
	<PERSON><PERSON>,
	<PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	InlineLoader,
} from '@/components/atoms';
import { useAuth } from '@/hooks/useAuth';

export default function RegisterForm({
	invitation,
	invitationError,
	isLoadingInvitation = false,
}: {
	invitation?: {
		token?: string;
		email?: string;
		status?: string;
	};
	invitationError?: string;
	isLoadingInvitation?: boolean;
}) {
	const { isLoading, error, clearError, register } = useAuth();

	const onSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
		event.preventDefault();
		clearError();

		const formData = new FormData(event.currentTarget);
		const fullName = formData.get('fullName') as string;
		const email =
			(formData.get('email') as string) ||
			(formData.get('invitationEmail') as string) ||
			'';
		const password = formData.get('password') as string;

		console.log('Register form submission:', {
			fullName,
			email,
			password,
			invitation,
		});

		if (!fullName || !email || !password) {
			console.log('Missing required fields');
			return;
		}

		const credentials = {
			fullName,
			email,
			password,
			...(invitation?.token && { invitationToken: invitation.token }),
		};

		console.log('Calling register with credentials:', credentials);
		try {
			const result = await register(credentials);
			console.log('Register result:', result);
		} catch (error) {
			console.error('Register error:', error);
		}
	};
	return (
		<div>
			{isLoadingInvitation && (
				<Alert
					className='mb-4'
					variant='default'>
					<InlineLoader text='Validating invitation...' />
				</Alert>
			)}
			{!isLoadingInvitation && invitation?.status === 'PENDING' && (
				<Alert
					className='mb-4'
					variant='default'>
					You have been invited. Please fill in your details to create an
					account.
				</Alert>
			)}
			{!isLoadingInvitation && invitationError && (
				<Alert
					className='mb-4'
					variant='error'>
					{invitationError}
				</Alert>
			)}
			{error && (
				<Alert
					className='mb-4'
					variant='error'>
					{error}
				</Alert>
			)}
			<form
				onSubmit={onSubmit}
				className='space-y-6'>
				{/* Hidden field for invitation email */}
				{invitation?.email && (
					<input
						type='hidden'
						name='invitationEmail'
						value={invitation.email}
					/>
				)}
				<Fieldset className='space-y-4'>
					<Legend className='text-lg mb-6 font-semibold text-foreground'>
						Create an account
					</Legend>
					<Field>
						<Label
							htmlFor='fullName'
							className='block text-sm/6 font-medium text-muted-foreground'>
							Full Name
						</Label>
						<Input
							id='fullName'
							name='fullName'
							type='text'
							required
							autoComplete='fullName'
							placeholder='John Doe'
							className='mt-2'
						/>
					</Field>
					<Field>
						<Label
							htmlFor='email'
							className='block text-sm/6 font-medium text-muted-foreground'>
							Email address
						</Label>

						<Input
							id='email'
							name='email'
							type='email'
							defaultValue={invitation?.email || ''}
							disabled={!!invitation?.email}
							required
							autoComplete='email'
							placeholder='<EMAIL>'
							className='mt-2'
						/>
					</Field>

					<Field>
						<Label
							htmlFor='password'
							className='block text-sm/6 font-medium text-muted-foreground'>
							Password
						</Label>
						<Input
							id='password'
							name='password'
							type='password'
							required
							autoComplete='current-password'
							placeholder='●●●●●●●●'
							className='mt-2'
						/>
					</Field>
				</Fieldset>

				<Button
					variant='primary'
					disabled={isLoading}
					type='submit'
					className='w-full'>
					{isLoading && <ButtonLoader />}
					{isLoading ? 'Creating account...' : 'Register'}
				</Button>
			</form>
		</div>
	);
}
