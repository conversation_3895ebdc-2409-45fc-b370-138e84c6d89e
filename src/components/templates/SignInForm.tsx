import { <PERSON>, Label, Fieldset, Legend } from '@headlessui/react';
import { Input, <PERSON><PERSON>, <PERSON><PERSON>, ButtonLoader } from '@/components/atoms';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useAuthStore } from '@/providers/auth-store-provider';
import { AuthAPI } from '@/api/AuthApi';
import {
	isApiSuccess,
	extractAuthTokens,
	getApiErrorMessage,
} from '@/common/utils/apiResponse';

export default function SignInForm() {
	const [message, setMessage] = useState<string | null>(null);
	const [localError, setLocalError] = useState<string | null>(null);
	const [isSubmitting, setIsSubmitting] = useState(false);
	const router = useRouter();
	const error = useAuthStore((state) => state.error);
	const clearError = useAuthStore((state) => state.clearError);
	const setTokens = useAuthStore((state) => state.setTokens);

	useEffect(() => {
		const urlMessage = router.query.message as string;
		if (urlMessage) {
			setMessage(urlMessage);
		}
	}, [router.query.message]);

	const onSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
		event.preventDefault();
		clearError();
		setLocalError(null);
		setIsSubmitting(true);

		const formData = new FormData(event.currentTarget);
		const email = formData.get('email') as string;
		const password = formData.get('password') as string;

		if (!email || !password) {
			setLocalError('Please fill in all fields');
			setIsSubmitting(false);
			return;
		}

		try {
			const authApi = new AuthAPI();
			const response = await authApi.login({ email, password });

			if (isApiSuccess(response)) {
				// Extract tokens from response using standardized utilities
				const { accessToken, refreshToken } = extractAuthTokens(response);

				if (!accessToken || !refreshToken) {
					setLocalError(
						'Invalid response from server: missing authentication tokens',
					);
					setIsSubmitting(false);
					return;
				}

				try {
					// Use the store's setTokens method
					await setTokens(accessToken, refreshToken);

					// Redirect to intended page or home
					const redirect = router.query.redirect as string;
					router.push(redirect || '/');
				} catch (tokenError) {
					console.error('Token validation error:', tokenError);
					setLocalError('Authentication failed: Invalid tokens received');
					setIsSubmitting(false);
				}
			} else {
				// Handle API error responses using standardized utilities
				const errorMessage = getApiErrorMessage(response);
				setLocalError(errorMessage);
				setIsSubmitting(false);
			}
		} catch (error) {
			console.error('Login error:', error);
			// Handle network or other errors
			if (error instanceof Error) {
				setLocalError(`Login failed: ${error.message}`);
			} else {
				setLocalError('Login failed: Network error or server unavailable');
			}
			setIsSubmitting(false);
		}
	};
	return (
		<div>
			{message && (
				<Alert
					className='mb-4'
					variant='default'>
					{message}
				</Alert>
			)}
			{(error || localError) && (
				<Alert
					className='mb-4'
					variant='error'>
					{localError || error}
				</Alert>
			)}
			<form
				onSubmit={onSubmit}
				className='space-y-6'>
				<Fieldset className='space-y-4'>
					<Legend className='text-lg font-semibold text-foreground'>
						Sign in to your account
					</Legend>

					<Field>
						<Label className='block text-sm/6 font-medium text-muted-foreground'>
							Email address
						</Label>

						<Input
							id='email'
							name='email'
							type='email'
							required
							autoComplete='email'
							placeholder='<EMAIL>'
							className='mt-2'
						/>
					</Field>

					<Field>
						<Label className='block text-sm/6 font-medium text-muted-foreground'>
							Password
						</Label>
						<Input
							id='password'
							name='password'
							type='password'
							required
							autoComplete='current-password'
							placeholder='●●●●●●●●'
							className='mt-2'
						/>
					</Field>
				</Fieldset>

				<Button
					variant='primary'
					disabled={isSubmitting}
					type='submit'
					className='w-full'>
					{isSubmitting && <ButtonLoader />}
					{isSubmitting ? 'Signing in...' : 'Sign in'}
				</Button>
			</form>
		</div>
	);
}
