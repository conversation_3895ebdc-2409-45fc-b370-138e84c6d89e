'use client';

import { MoonIcon, SunIcon } from '@heroicons/react/24/solid';
import { useTheme } from 'next-themes';
import { Button } from '@/components/atoms';

export default function ThemeToggle() {
	const { setTheme, theme } = useTheme();

	return (
		<Button
			variant='ghost'
			size='sm'
			onClick={() => setTheme(theme === 'light' ? 'dark' : 'light')}
			aria-label={`Switch to ${theme === 'light' ? 'dark' : 'light'} mode`}>
			{theme === 'light' ? (
				<MoonIcon className='w-4 h-4 text-text-secondary' />
			) : (
				<SunIcon className='w-4 h-4 text-text-secondary' />
			)}
		</Button>
	);
}
