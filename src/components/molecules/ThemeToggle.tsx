'use client';

import { MoonIcon, SunIcon } from '@heroicons/react/24/solid';
import { useTheme } from 'next-themes';
import { Button } from '@/components/atoms';

export default function ThemeToggle() {
	const { setTheme, theme } = useTheme();

	return (
		<Button
			variant='ghost'
			onClick={() => setTheme(theme === 'light' ? 'dark' : 'light')}>
			{theme === 'light' ? (
				<SunIcon className='h-[1.2rem] w-[1.2rem] fill-yellow-500' />
			) : (
				<MoonIcon className='h-[1.2rem] w-[1.2rem] fill-blue-400' />
			)}
		</Button>
	);
}
