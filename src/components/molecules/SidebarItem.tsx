import { Button } from '../atoms';

export interface SidebarItemProps {
	icon: React.ReactNode;
	label: string;
	isActive?: boolean;
	isCollapsed?: boolean;
	onClick?: () => void;
}

const SidebarItem: React.FC<SidebarItemProps> = ({
	icon,
	label,
	isActive = false,
	isCollapsed = false,
	onClick,
}) => {
	return (
		<Button
			variant={isActive ? 'primary' : 'ghost'}
			onClick={onClick}
			className={`${
				isCollapsed
					? 'justify-center'
					: 'justify-start w-full text-sm font-medium'
			} `}
			aria-label={label}>
			<div className={`h-4 w-4 ${isCollapsed ? 'mr-0' : 'mr-2'}`}>{icon}</div>
			{!isCollapsed && <span className='text-sm'>{label}</span>}
		</Button>
	);
};
SidebarItem.displayName = 'SidebarItem';
export default SidebarItem;
