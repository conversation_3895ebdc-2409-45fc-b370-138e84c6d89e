import { clsx } from 'clsx';

export interface SidebarItemProps {
	icon: React.ReactNode;
	label: string;
	isActive?: boolean;
	isCollapsed?: boolean;
	onClick?: () => void;
	href?: string;
	badge?: React.ReactNode;
	disabled?: boolean;
}

const SidebarItem: React.FC<SidebarItemProps> = ({
	icon,
	label,
	isActive = false,
	isCollapsed = false,
	onClick,
	href,
	badge,
	disabled = false,
}) => {
	const baseClasses = clsx(
		// Layout
		'w-full flex items-center',
		'transition-all duration-200 ease-in-out',
		'rounded-medium',
		// Spacing
		isCollapsed ? 'justify-center p-100' : 'justify-start px-150 py-100',
		// States
		isActive && [
			'bg-accent-background-default text-text-inverse',
			'shadow-small',
		],
		!isActive && [
			'text-text-secondary hover:text-text-primary',
			'hover:bg-background-layer-1',
		],
		disabled && 'opacity-50 cursor-not-allowed',
		// Focus styles
		'focus:outline-none focus:ring-2 focus:ring-accent-border-focus focus:ring-offset-1',
	);

	const content = (
		<>
			<span className={clsx('flex-shrink-0 w-4 h-4', !isCollapsed && 'mr-150')}>
				{icon}
			</span>
			{!isCollapsed && (
				<>
					<span className='text-75 font-medium truncate flex-1'>{label}</span>
					{badge && <span className='ml-100 flex-shrink-0'>{badge}</span>}
				</>
			)}
		</>
	);

	if (href) {
		return (
			<a
				href={href}
				className={baseClasses}
				onClick={onClick}
				aria-label={isCollapsed ? label : undefined}
				title={isCollapsed ? label : undefined}>
				{content}
			</a>
		);
	}

	return (
		<button
			type='button'
			className={baseClasses}
			onClick={onClick}
			disabled={disabled}
			aria-label={isCollapsed ? label : undefined}
			title={isCollapsed ? label : undefined}>
			{content}
		</button>
	);
};
SidebarItem.displayName = 'SidebarItem';
export default SidebarItem;
