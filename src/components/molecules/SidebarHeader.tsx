import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/solid';
import { Button } from '../atoms';

export interface SidebarHeaderProps {
	title: string;
	subtitle?: string;
	isCollapsed?: boolean;
	onToggle: () => void;
}

const SidebarHeader: React.FC<SidebarHeaderProps> = ({
	title,
	subtitle,
	isCollapsed = false,
	onToggle,
}) => {
	return (
		<div
			className={`flex items-center ${
				isCollapsed ? 'justify-center' : 'justify-between'
			} p-4`}>
			<div>
				{!isCollapsed && (
					<div className='flex items-center'>
						<h1 className='text-lg font-semibold text-foreground'>{title}</h1>
					</div>
				)}
				{subtitle && (
					<p className='text-sm text-muted-foreground'>{subtitle}</p>
				)}
			</div>
			<div className='flex-shrink-0'>
				<Button
					variant='secondary'
					onClick={onToggle}
					aria-label={isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}>
					<div className='h-3 w-3'>
						{isCollapsed ? <ChevronRightIcon /> : <ChevronLeftIcon />}
					</div>
				</Button>
			</div>
		</div>
	);
};

SidebarHeader.displayName = 'SidebarHeader';
export default SidebarHeader;
