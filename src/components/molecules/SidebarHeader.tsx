import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/solid';
import { clsx } from 'clsx';
import { Button, Typography } from '../atoms';

export interface SidebarHeaderProps {
	title: string;
	subtitle?: string;
	isCollapsed?: boolean;
	onToggle: () => void;
}

const SidebarHeader: React.FC<SidebarHeaderProps> = ({
	title,
	subtitle,
	isCollapsed = false,
	onToggle,
}) => {
	return (
		<header
			className={clsx(
				'flex items-center border-b border-border-subtle',
				'p-200 min-h-16',
				isCollapsed ? 'justify-center' : 'justify-between',
			)}>
			{!isCollapsed && (
				<div className='flex-1 min-w-0'>
					<Typography
						variant='h5'
						weight='semibold'
						className='truncate'>
						{title}
					</Typography>
					{subtitle && (
						<Typography
							variant='caption'
							color='secondary'
							className='truncate mt-25'>
							{subtitle}
						</Typography>
					)}
				</div>
			)}

			<div className='flex-shrink-0'>
				<Button
					variant='ghost'
					size='sm'
					onClick={onToggle}
					className='p-100'
					aria-label={isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}>
					{isCollapsed ? (
						<ChevronRightIcon className='w-4 h-4' />
					) : (
						<ChevronLeftIcon className='w-4 h-4' />
					)}
				</Button>
			</div>
		</header>
	);
};

SidebarHeader.displayName = 'SidebarHeader';
export default SidebarHeader;
