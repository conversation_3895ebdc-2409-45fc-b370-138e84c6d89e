import { clsx } from 'clsx';
import { Typography } from '../atoms';
import SidebarItem, { SidebarItemProps } from './SidebarItem';

export interface SidebarSectionProps {
	title: string;
	items?: SidebarItemProps[];
	isCollapsed?: boolean;
}

const SidebarSection: React.FC<SidebarSectionProps> = ({
	title,
	items,
	isCollapsed = false,
}) => {
	if (!items || items.length === 0) {
		return null;
	}

	return (
		<section className={clsx('px-150 py-200')}>
			{!isCollapsed && title && (
				<Typography
					variant='overline'
					color='tertiary'
					className='mb-100 px-150'>
					{title}
				</Typography>
			)}
			<nav className='space-y-50'>
				{items.map((item, index) => (
					<SidebarItem
						key={item.label || index}
						isCollapsed={isCollapsed}
						{...item}
					/>
				))}
			</nav>
		</section>
	);
};

SidebarSection.displayName = 'SidebarSection';
export default SidebarSection;
