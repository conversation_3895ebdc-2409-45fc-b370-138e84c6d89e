import SidebarItem, { SidebarItemProps } from './SidebarItem';

export interface SidebarSectionProps {
	title: string;
	items?: SidebarItemProps[];
	isCollapsed?: boolean;
}

const SidebarSection: React.FC<SidebarSectionProps> = ({
	title,
	items,
	isCollapsed = false,
}) => {
	return (
		<div className={`px-3 py-2 ${isCollapsed ? 'collapsed' : ''}`}>
			{!isCollapsed && (
				<h2 className='uppercase tracking-wide font-bold text-xs text-muted-foreground mb-2 px-3'>
					{title}
				</h2>
			)}
			<div className='space-y-1 flex flex-col justify-center'>
				{items?.map((item, index) => (
					<SidebarItem
						key={index}
						isCollapsed={isCollapsed}
						{...item}
					/>
				))}
			</div>
		</div>
	);
};

SidebarSection.displayName = 'SidebarSection';
export default SidebarSection;
