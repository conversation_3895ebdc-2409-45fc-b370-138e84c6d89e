import React from 'react';
import { clsx } from 'clsx';

type TypographyVariant =
	| 'h1'
	| 'h2'
	| 'h3'
	| 'h4'
	| 'h5'
	| 'h6'
	| 'body-lg'
	| 'body'
	| 'body-sm'
	| 'caption'
	| 'overline'
	| 'display-lg'
	| 'display'
	| 'display-sm';

type TypographyWeight =
	| 'light'
	| 'regular'
	| 'medium'
	| 'semibold'
	| 'bold'
	| 'extrabold';

type TypographyAlign = 'left' | 'center' | 'right' | 'justify';

type TypographyColor =
	| 'primary'
	| 'secondary'
	| 'tertiary'
	| 'disabled'
	| 'inverse'
	| 'accent'
	| 'positive'
	| 'negative'
	| 'notice'
	| 'informative';

interface TypographyProps {
	variant?: TypographyVariant;
	weight?: TypographyWeight;
	align?: TypographyAlign;
	color?: TypographyColor;
	responsive?: boolean;
	className?: string;
	children: React.ReactNode;
	as?: keyof React.JSX.IntrinsicElements;
}

const variantStyles = {
	// Display variants - largest text
	'display-lg': 'text-responsive-5xl font-bold',
	display: 'text-responsive-4xl font-bold',
	'display-sm': 'text-responsive-3xl font-bold',

	// Heading variants
	h1: 'text-responsive-3xl font-bold',
	h2: 'text-responsive-2xl font-semibold',
	h3: 'text-responsive-xl font-semibold',
	h4: 'text-responsive-lg font-medium',
	h5: 'text-responsive-base font-medium',
	h6: 'text-responsive-sm font-medium',

	// Body variants
	'body-lg': 'text-responsive-lg font-regular',
	body: 'text-responsive-base font-regular',
	'body-sm': 'text-responsive-sm font-regular',

	// Utility variants
	caption: 'text-responsive-xs font-regular',
	overline: 'text-responsive-xs font-medium uppercase tracking-wide',
};

const nonResponsiveVariantStyles = {
	// Display variants - largest text
	'display-lg': 'text-900 font-bold',
	display: 'text-800 font-bold',
	'display-sm': 'text-700 font-bold',

	// Heading variants
	h1: 'text-700 font-bold',
	h2: 'text-600 font-semibold',
	h3: 'text-500 font-semibold',
	h4: 'text-400 font-medium',
	h5: 'text-300 font-medium',
	h6: 'text-200 font-medium',

	// Body variants
	'body-lg': 'text-200 font-regular',
	body: 'text-100 font-regular',
	'body-sm': 'text-75 font-regular',

	// Utility variants
	caption: 'text-50 font-regular',
	overline: 'text-50 font-medium uppercase tracking-wide',
};

const weightStyles = {
	light: 'font-light',
	regular: 'font-regular',
	medium: 'font-medium',
	semibold: 'font-semibold',
	bold: 'font-bold',
	extrabold: 'font-extrabold',
};

const alignStyles = {
	left: 'text-left',
	center: 'text-center',
	right: 'text-right',
	justify: 'text-justify',
};

const colorStyles = {
	primary: 'text-text-primary',
	secondary: 'text-text-secondary',
	tertiary: 'text-text-tertiary',
	disabled: 'text-text-disabled',
	inverse: 'text-text-inverse',
	accent: 'text-accent-content-default',
	positive: 'text-positive-content',
	negative: 'text-negative-content',
	notice: 'text-notice-content',
	informative: 'text-informative-content',
};

const defaultElements: Record<
	TypographyVariant,
	keyof React.JSX.IntrinsicElements
> = {
	'display-lg': 'h1',
	display: 'h1',
	'display-sm': 'h1',
	h1: 'h1',
	h2: 'h2',
	h3: 'h3',
	h4: 'h4',
	h5: 'h5',
	h6: 'h6',
	'body-lg': 'p',
	body: 'p',
	'body-sm': 'p',
	caption: 'span',
	overline: 'span',
};

const Typography: React.FC<TypographyProps> = ({
	variant = 'body',
	weight,
	align = 'left',
	color = 'primary',
	responsive = true,
	className,
	children,
	as,
	...props
}) => {
	const Element = as || defaultElements[variant];
	const variantStyleMap = responsive
		? variantStyles
		: nonResponsiveVariantStyles;

	return (
		<Element
			className={clsx(
				// Base styles
				'transition-colors duration-medium ease-in-out',
				// Variant styles (includes default font-weight)
				variantStyleMap[variant],
				// Override weight if specified
				weight && weightStyles[weight],
				// Alignment
				alignStyles[align],
				// Color
				colorStyles[color],
				className,
			)}
			{...props}>
			{children}
		</Element>
	);
};

Typography.displayName = 'Typography';

export default Typography;
export type {
	TypographyProps,
	TypographyVariant,
	TypographyWeight,
	TypographyAlign,
	TypographyColor,
};
