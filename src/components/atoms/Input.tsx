import { Input as HeadlessInput } from '@headlessui/react';
import { forwardRef } from 'react';
import { clsx } from 'clsx';

interface InputProps
	extends React.ComponentProps<typeof HeadlessInput>,
		Omit<
			React.InputHTMLAttributes<HTMLInputElement>,
			keyof React.ComponentProps<typeof HeadlessInput> | 'size'
		> {
	className?: string;
	inputSize?: 'small' | 'medium' | 'large';
	variant?: 'default' | 'error';
}

const inputSizes = {
	small: 'px-150 py-75 text-75 h-input-small',
	medium: 'px-200 py-100 text-100 h-input-medium',
	large: 'px-300 py-150 text-200 h-input-large',
};

const inputVariants = {
	default: [
		'bg-surface-base text-text-primary',
		'border border-border-base',
		'focus:border-accent-border-focus focus:ring-1 focus:ring-accent-border-focus',
	],
	error: [
		'bg-surface-base text-text-primary',
		'border border-negative-border',
		'focus:border-negative-border focus:ring-1 focus:ring-negative-border',
	],
};

const Input = forwardRef<HTMLInputElement, InputProps>(
	({ className, inputSize = 'medium', variant = 'default', ...props }, ref) => {
		return (
			<HeadlessInput
				ref={ref}
				className={clsx(
					// Base styles
					'block w-full rounded-medium',
					'transition-all duration-medium ease-in-out',
					'focus:outline-none',
					// Placeholder styles
					'placeholder:text-text-tertiary',
					// Disabled styles
					'disabled:cursor-not-allowed disabled:opacity-50',
					'disabled:bg-background-layer-1',
					// Size styles
					inputSizes[inputSize],
					// Variant styles
					inputVariants[variant],
					className,
				)}
				{...props}
			/>
		);
	},
);

Input.displayName = 'Input';
export default Input;
