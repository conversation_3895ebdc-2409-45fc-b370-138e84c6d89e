import { Input as HeadlessInput } from '@headlessui/react';
import { forwardRef } from 'react';
import { clsx } from 'clsx';

interface InputProps
	extends React.ComponentProps<typeof HeadlessInput>,
		Omit<
			React.InputHTMLAttributes<HTMLInputElement>,
			keyof React.ComponentProps<typeof HeadlessInput>
		> {
	className?: string;
}

const Input = forwardRef<HTMLInputElement, InputProps>(
	({ className, ...props }, ref) => {
		return (
			<HeadlessInput
				ref={ref}
				className={clsx(
					// Base styles
					'block w-full rounded-md px-3 py-1.5 text-base sm:text-sm',
					// Background and text using theme tokens
					'bg-background text-foreground',
					// Border using theme tokens
					'border border-input',
					// Placeholder using theme tokens
					'placeholder:text-placeholder',
					// Focus styles using theme tokens
					'focus:border-ring focus:ring-1 focus:ring-ring focus:outline-none',
					// Disabled styles
					'disabled:cursor-not-allowed disabled:opacity-50',
					// Smooth transitions
					'transition-colors',
					className,
				)}
				{...props}
			/>
		);
	},
);

Input.displayName = 'Input';
export default Input;
