import { Button as HeadlessButton } from '@headlessui/react';
import { forwardRef } from 'react';
import { clsx } from 'clsx';

interface ButtonProps
	extends React.ComponentProps<typeof HeadlessButton>,
		Omit<
			React.ButtonHTMLAttributes<HTMLButtonElement>,
			keyof React.ComponentProps<typeof HeadlessButton>
		> {
	className?: string;
	children: React.ReactNode;
	variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive';
	size?: 'sm' | 'md' | 'lg';
	icon?: React.ReactNode;
}

const buttonVariants = {
	primary: [
		'bg-accent-background-default text-text-inverse',
		'hover:bg-accent-background-hover',
		'active:bg-accent-background-down',
		'focus:ring-accent-border-focus',
	],
	secondary: [
		'bg-background-layer-1 text-text-primary border border-border-base',
		'hover:bg-background-layer-2 hover:border-border-strong',
		'active:bg-background-layer-1',
		'focus:ring-accent-border-focus',
	],
	outline: [
		'bg-transparent text-accent-content-default border border-accent-border-default',
		'hover:bg-accent-background-default/10 hover:border-accent-border-hover',
		'active:bg-accent-background-default/20',
		'focus:ring-accent-border-focus',
	],
	ghost: [
		'bg-transparent text-text-secondary',
		'hover:bg-background-layer-1 hover:text-text-primary',
		'active:bg-background-layer-2',
		'focus:ring-accent-border-focus',
	],
	destructive: [
		'bg-negative-content text-text-inverse',
		'hover:bg-negative-content/90',
		'active:bg-negative-content/80',
		'focus:ring-negative-border',
	],
};

const buttonSizes = {
	sm: 'px-150 py-75 text-responsive-sm h-button-small',
	md: 'px-200 py-100 text-responsive-base h-button-medium',
	lg: 'px-300 py-150 text-responsive-lg h-button-large',
};

const Button = forwardRef<HTMLButtonElement, ButtonProps>(
	(
		{ className, children, variant = 'primary', size = 'md', icon, ...props },
		ref,
	) => {
		return (
			<HeadlessButton
				ref={ref}
				className={clsx(
					// Base styles
					'inline-flex items-center justify-center font-medium',
					'rounded-button transition-all duration-medium ease-in-out',
					'focus:outline-none focus:ring-2 focus:ring-offset-2',
					'focus:ring-offset-background-base',
					'disabled:opacity-50 disabled:cursor-not-allowed',
					// Variant styles
					buttonVariants[variant],
					// Size styles
					buttonSizes[size],
					className,
				)}
				{...props}>
				{icon && <span className='mr-100'>{icon}</span>}
				{children}
			</HeadlessButton>
		);
	},
);

Button.displayName = 'Button';

export default Button;
