import { Button as HeadlessButton } from '@headlessui/react';
import { forwardRef } from 'react';
import { clsx } from 'clsx';

interface ButtonProps
	extends React.ComponentProps<typeof HeadlessButton>,
		Omit<
			React.ButtonHTMLAttributes<HTMLButtonElement>,
			keyof React.ComponentProps<typeof HeadlessButton>
		> {
	className?: string;
	children: React.ReactNode;
	variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive';
	size?: 'sm' | 'md' | 'lg';
	icon?: React.ReactNode;
}

const buttonVariants = {
	primary: 'bg-primary text-primary-foreground hover:bg-primary/90',
	secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
	outline:
		'border border-input bg-background hover:bg-accent hover:text-accent-foreground',
	ghost: 'hover:bg-accent hover:text-accent-foreground',
	destructive:
		'bg-destructive text-destructive-foreground hover:bg-destructive/90',
};

const buttonSizes = {
	sm: 'px-3 py-1.5 text-sm',
	md: 'px-4 py-2 text-sm',
	lg: 'px-6 py-3 text-base',
};

const Button = forwardRef<HTMLButtonElement, ButtonProps>(
	(
		{ className, children, variant = 'primary', size = 'md', icon, ...props },
		ref,
	) => {
		return (
			<HeadlessButton
				ref={ref}
				className={clsx(
					// Base styles
					'inline-flex items-center justify-center font-medium rounded-md',
					'focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 focus:ring-offset-background',
					'disabled:opacity-50 disabled:cursor-not-allowed',
					'transition-colors',
					// Variant styles
					buttonVariants[variant],
					// Size styles
					buttonSizes[size],
					className,
				)}
				{...props}>
				{icon && <span className='mr-2'>{icon}</span>}
				{children}
			</HeadlessButton>
		);
	},
);

Button.displayName = 'Button';

export default Button;
