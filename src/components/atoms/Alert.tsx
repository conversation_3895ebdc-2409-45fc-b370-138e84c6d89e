interface AlertProps extends React.HTMLAttributes<HTMLDivElement> {
	className?: string;
	children: React.ReactNode;
	variant?: 'default' | 'info' | 'success' | 'warning' | 'error';
}

const alertVariants = {
	default:
		'bg-secondary text-secondary-foreground border border-secondary-muted',
	info: 'bg-info text-info-foreground border border-info-muted',
	success: 'bg-success text-success-foreground border border-success-muted',
	warning: 'bg-warning text-warning-foreground border border-warning-muted',
	error:
		'bg-destructive text-destructive-foreground border border-destructive-muted',
};

const Alert: React.FC<AlertProps> = ({
	className,
	children,
	variant = 'default',
	...props
}) => {
	return (
		<div
			className={`py-2 px-3 rounded-md shadow-sm ${alertVariants[variant]} ${className}`}
			{...props}>
			{children}
		</div>
	);
};

Alert.displayName = 'Alert';
export default Alert;
