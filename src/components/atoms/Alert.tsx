import { clsx } from 'clsx';

interface AlertProps extends React.HTMLAttributes<HTMLDivElement> {
	className?: string;
	children: React.ReactNode;
	variant?: 'default' | 'informative' | 'positive' | 'notice' | 'negative';
	size?: 'small' | 'medium' | 'large';
	icon?: React.ReactNode;
}

const alertVariants = {
	default: [
		'bg-background-layer-1 text-text-primary',
		'border border-border-base',
	],
	informative: [
		'bg-informative-background text-informative-content',
		'border border-informative-border',
	],
	positive: [
		'bg-positive-background text-positive-content',
		'border border-positive-border',
	],
	notice: [
		'bg-notice-background text-notice-content',
		'border border-notice-border',
	],
	negative: [
		'bg-negative-background text-negative-content',
		'border border-negative-border',
	],
};

const alertSizes = {
	small: 'p-150 text-75',
	medium: 'p-200 text-100',
	large: 'p-300 text-200',
};

const Alert: React.FC<AlertProps> = ({
	className,
	children,
	variant = 'default',
	size = 'medium',
	icon,
	...props
}) => {
	return (
		<div
			className={clsx(
				// Base styles
				'rounded shadow-small',
				'transition-all duration-medium ease-in-out',
				// Variant styles
				alertVariants[variant],
				// Size styles
				alertSizes[size],
				className,
			)}
			role='alert'
			{...props}>
			<div className='flex items-start gap-150'>
				{icon && <span className='flex-shrink-0 w-4 h-4 mt-25'>{icon}</span>}
				<div className='flex-1'>{children}</div>
			</div>
		</div>
	);
};

Alert.displayName = 'Alert';
export default Alert;
