import { Fragment } from 'react';
import SidebarSection, {
	SidebarSectionProps,
} from '../molecules/SidebarSection';

import { SidebarHeader } from '../molecules';
import { Divider } from '../atoms';

interface SidebarProps {
	title: string;
	subtitle?: string;
	sections: SidebarSectionProps[];
	isCollapsed?: boolean;
	onToggle?: () => void;
	onItemClick?: (itemId: string) => void;
	className?: string;
}

const Sidebar: React.FC<SidebarProps> = ({
	title,
	subtitle,
	sections,
	isCollapsed = false,
	onToggle,
	className = '',
}) => {
	return (
		<div
			className={`bg-background border-r border-accent flex flex-col h-full transition-all duration-300 ${
				isCollapsed ? 'w-20' : 'w-64'
			} ${className}`}>
			<SidebarHeader
				title={title}
				subtitle={subtitle}
				isCollapsed={isCollapsed}
				onToggle={onToggle!!}
			/>

			<div className='flex-1 overflow-y-auto'>
				{sections.map((section, index) => (
					<Fragment key={index}>
						{index > 0 && <Divider className='my-2' />}
						<SidebarSection
							title={section.title!!}
							isCollapsed={isCollapsed}
							items={section.items}
						/>
					</Fragment>
				))}
			</div>
		</div>
	);
};

Sidebar.displayName = 'Sidebar';
export default Sidebar;
