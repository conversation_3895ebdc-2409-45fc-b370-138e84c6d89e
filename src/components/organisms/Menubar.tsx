import Link from 'next/link';
import { Button } from '@/components/atoms';
import { useAuthStore } from '@/providers/auth-store-provider';

interface MenubarProps {
	logo: React.ReactNode;
	links: {
		title: string;
		href: string;
		startIcon?: React.ReactNode;
		endIcon?: React.ReactNode;
		isActive?: boolean;
		onClick?: () => void;
		className?: string;
		disabled?: boolean;
	}[];
}

const Menubar: React.FC<MenubarProps> = ({ logo, links }) => {
	const user = useAuthStore((state) => state.user);
	const logout = useAuthStore((state) => state.logout);
	const isLoading = useAuthStore((state) => state.isLoading);

	return (
		<header className='bg-surface-base border-b border-border-base flex items-center justify-between h-16 px-200 shadow-small'>
			<div className='flex items-center gap-200'>
				<div className='flex-shrink-0 text-200 font-semibold text-text-primary'>
					{logo}
				</div>
				<nav className='flex items-center gap-150'>
					{links.map((link, index) => (
						<Link
							key={index}
							href={link.href}
							className={`flex items-center gap-100 px-150 py-75 rounded-medium transition-colors duration-medium ${
								link.isActive
									? 'bg-accent-background-default/10 text-accent-content-default'
									: 'text-text-secondary hover:text-text-primary hover:bg-background-layer-1'
							} ${link.className}`}
							onClick={link.onClick}
							aria-disabled={link.disabled}>
							{link.startIcon && (
								<span className='flex-shrink-0 w-4 h-4'>{link.startIcon}</span>
							)}
							<span className='text-100 font-medium'>{link.title}</span>
							{link.endIcon && (
								<span className='flex-shrink-0 w-4 h-4'>{link.endIcon}</span>
							)}
						</Link>
					))}
				</nav>
			</div>

			{user && (
				<div className='flex items-center gap-200'>
					<span className='text-100 text-text-secondary'>{user.fullName}</span>
					<Button
						variant='ghost'
						size='sm'
						onClick={logout}
						disabled={isLoading}>
						{isLoading ? 'Logging out...' : 'Logout'}
					</Button>
				</div>
			)}
		</header>
	);
};

Menubar.displayName = 'Menubar';
export default Menubar;
