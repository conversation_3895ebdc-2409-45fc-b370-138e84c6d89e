import { useState } from 'react';
import { useRouter } from 'next/router';
import { AuthAPI } from '@/api/AuthApi';
import { useAuthStore, getAuthStore } from '@/providers/auth-store-provider';
import { Httpstatus } from '@/common/StandardApi';

interface LoginCredentials {
	email: string;
	password: string;
}

interface RegisterCredentials {
	fullName: string;
	email: string;
	password: string;
	invitationToken?: string;
}

interface UserData {
	id: string;
	fullName?: string;
	email: string;
	role: 'ADMIN' | 'USER';
	status: 'ACTIVE' | 'INACTIVE' | 'REJECTED';
}

export const useAuth = () => {
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const router = useRouter();
	const setTokens = useAuthStore((state) => state.setTokens);
	const setUser = useAuthStore((state) => state.setUser);
	const authLogout = useAuthStore((state) => state.logout);

	const clearError = () => setError(null);

	const extractTokens = (response: { data: unknown }) => {
		let accessToken, refreshToken;

		// According to backend API spec, tokens are returned in response.data.data
		const responseData = response.data as {
			data?: { accessToken?: string; refreshToken?: string };
		};
		if (responseData?.data?.accessToken) {
			accessToken = responseData.data.accessToken;
			refreshToken = responseData.data.refreshToken;
		}

		return { accessToken, refreshToken };
	};

	const authenticateUser = async (
		accessToken: string,
		refreshToken: string,
		userData?: UserData,
	) => {
		console.log('authenticateUser called with:', {
			accessToken: accessToken ? 'present' : 'missing',
			refreshToken: refreshToken ? 'present' : 'missing',
			userData,
		});

		if (
			accessToken &&
			refreshToken &&
			typeof accessToken === 'string' &&
			typeof refreshToken === 'string'
		) {
			try {
				console.log('Setting tokens...');
				// setTokens no longer throws errors, so we need to check the store state
				await setTokens(accessToken, refreshToken);

				console.log('Tokens set, waiting for store update...');
				// Give a moment for the store to update
				await new Promise((resolve) => setTimeout(resolve, 100));

				// Check if there's a store error after setting tokens
				try {
					const authStore = getAuthStore();
					const currentStoreError = authStore.getState().error;
					console.log('Store error after setting tokens:', currentStoreError);
					if (currentStoreError) {
						console.error('Token validation failed:', currentStoreError);
						setError(currentStoreError);
						return false;
					}
				} catch (storeAccessError) {
					console.warn(
						'Could not access auth store for error checking:',
						storeAccessError,
					);
					// Continue anyway, as the store might not be fully initialized
				}

				// If we have user data from the API response, update the user info
				if (userData) {
					setUser({
						id: userData.id,
						fullName: userData.fullName || userData.email?.split('@')[0] || '',
						email: userData.email,
						role: userData.role,
						status: userData.status,
					});
				}

				// Check if there's a redirect URL from middleware
				const redirectUrl = router.query.redirect as string;
				const destination =
					redirectUrl &&
					(redirectUrl.startsWith('/projects') ||
						redirectUrl.startsWith('/admin'))
						? redirectUrl
						: '/';

				console.log('Redirecting to:', destination);
				router.push(destination);
				console.log('authenticateUser returning true');
				return true;
			} catch (error) {
				console.error('Error in authenticateUser:', error);
				return false;
			}
		}
		console.log('authenticateUser returning false - invalid tokens');
		return false;
	};

	const login = async (credentials: LoginCredentials) => {
		setIsLoading(true);
		setError(null);

		try {
			const authApi = new AuthAPI();
			const response = await authApi.login(credentials);

			if (
				response.status === Httpstatus.SuccessOK ||
				response.status === Httpstatus.SuccessCreated
			) {
				const { accessToken, refreshToken } = extractTokens(response);
				const userData = (response.data as { data?: UserData })?.data; // Get user data from API response

				if (
					accessToken &&
					refreshToken &&
					(await authenticateUser(accessToken, refreshToken, userData))
				) {
					return { success: true };
				} else {
					setError('Login successful but tokens are invalid or missing.');
					return { success: false };
				}
			} else {
				const errorData = response.data as { message?: string; error?: string };
				setError(
					errorData?.message ||
						errorData?.error ||
						`Login failed (Status: ${response.status}). Please try again.`,
				);
				return { success: false };
			}
		} catch (error) {
			console.error('Login failed:', error);
			setError('An unexpected error occurred. Please try again.');
			return { success: false };
		} finally {
			setIsLoading(false);
		}
	};

	const register = async (credentials: RegisterCredentials) => {
		setIsLoading(true);
		setError(null);

		try {
			// Check if this is an invitation acceptance
			if (credentials.invitationToken) {
				console.log(
					'Processing invitation acceptance with credentials:',
					credentials,
				);
				const InvitationApi = (await import('@/api/InvitationApi')).default;
				const invitationApi = new InvitationApi();

				console.log('Calling acceptInvitation API...');
				const response = await invitationApi.acceptInvitation({
					token: credentials.invitationToken,
					password: credentials.password,
					fullName: credentials.fullName,
				});

				console.log('Invitation acceptance response:', response);

				if (
					response.status === Httpstatus.SuccessOK ||
					response.status === Httpstatus.SuccessCreated
				) {
					// Invitation accepted successfully, now auto-login
					console.log(
						'Invitation accepted successfully, attempting auto-login...',
					);
					try {
						const authApi = new AuthAPI();
						const loginResponse = await authApi.login({
							email: credentials.email,
							password: credentials.password,
						});

						console.log('Auto-login response:', loginResponse);

						if (
							loginResponse.status === Httpstatus.SuccessOK ||
							loginResponse.status === Httpstatus.SuccessCreated
						) {
							const { accessToken, refreshToken } =
								extractTokens(loginResponse);
							const userData = (loginResponse.data as { data?: UserData })
								?.data;

							console.log('Extracted tokens and user data:', {
								accessToken: !!accessToken,
								refreshToken: !!refreshToken,
								userData,
							});

							if (!accessToken || !refreshToken) {
								setError('Login successful but tokens are invalid or missing.');
								return { success: false };
							}

							const authResult = await authenticateUser(
								accessToken,
								refreshToken,
								userData,
							);
							console.log('Authentication result:', authResult);

							if (authResult) {
								return { success: true };
							} else {
								setError(
									'Authentication failed after invitation acceptance. Please try signing in manually.',
								);
								return { success: false };
							}
						} else {
							setError(
								'Auto-login failed after invitation acceptance. Please sign in manually.',
							);
							return { success: false };
						}
					} catch (loginError) {
						console.error(
							'Auto-login after invitation acceptance failed:',
							loginError,
						);
						setError(
							'Invitation accepted successfully, but auto-login failed. Please sign in manually.',
						);
						return { success: false };
					}
				} else {
					const errorData = response.data as {
						message?: string;
						error?: string;
					};
					setError(
						errorData?.message ||
							errorData?.error ||
							`Invitation acceptance failed (Status: ${response.status}). Please try again.`,
					);
					return { success: false };
				}
			} else {
				// Regular registration
				const authApi = new AuthAPI();
				const response = await authApi.register(credentials);

				if (response.status === Httpstatus.SuccessCreated) {
					// Registration successful, now auto-login as per user preference
					try {
						const loginResponse = await authApi.login({
							email: credentials.email,
							password: credentials.password,
						});

						if (
							loginResponse.status === Httpstatus.SuccessOK ||
							loginResponse.status === Httpstatus.SuccessCreated
						) {
							const { accessToken, refreshToken } =
								extractTokens(loginResponse);
							const userData = (loginResponse.data as { data?: UserData })
								?.data; // Get user data from login response

							if (
								accessToken &&
								refreshToken &&
								(await authenticateUser(accessToken, refreshToken, userData))
							) {
								return { success: true };
							}
						}
					} catch (loginError) {
						console.error('Auto-login failed:', loginError);
						setError(
							'Registration successful, but auto-login failed. Please sign in manually.',
						);
						return { success: false };
					}

					// Fallback: redirect to signin with success message
					router.push(
						'/signin?message=Registration successful. Please sign in.',
					);
					return { success: true, requiresSignin: true };
				} else {
					const errorData = response.data as {
						message?: string;
						error?: string;
					};
					setError(
						errorData?.message ||
							errorData?.error ||
							`Registration failed (Status: ${response.status}). Please try again.`,
					);
					return { success: false };
				}
			}
		} catch (error) {
			console.error('Registration failed:', error);
			setError('An unexpected error occurred. Please try again.');
			return { success: false };
		} finally {
			setIsLoading(false);
		}
	};

	const logout = async () => {
		setIsLoading(true);
		try {
			await authLogout();
		} catch (error) {
			console.error('Logout failed:', error);
		} finally {
			setIsLoading(false);
		}
	};

	return {
		isLoading,
		error,
		clearError,
		login,
		register,
		logout,
	};
};
