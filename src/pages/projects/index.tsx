import { ThemeToggle } from '@/components/molecules';
import { SideBar } from '@/components/organisms';
import { AppTemplate } from '@/components/templates';
import { NextPageWithLayout } from '@/pages/_app';
import {
	CircleStackIcon,
	FolderIcon,
	UserCircleIcon,
	PlusIcon,
} from '@heroicons/react/24/outline';
import { useState } from 'react';
import {
	Card,
	CardHeader,
	CardContent,
	CardFooter,
	CardTitle,
	CardDescription,
	Button,
	Badge,
} from '@/components/atoms';

const ProjectsPage: NextPageWithLayout = () => {
	const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
	const items = [
		{
			label: 'Project 1',
			icon: <FolderIcon />,
		},
		{
			label: 'Project 2',
			icon: <UserCircleIcon />,
		},
	];
	return (
		<div className='flex h-svh'>
			<SideBar
				title='Projects'
				sections={[
					{
						title: 'My Projects',
						items,
					},
					{
						title: 'Archived Projects',
						items: [
							{
								label: 'Archived Project 1',
								icon: <CircleStackIcon />,
								onClick: () => console.log('Archived Project 1 clicked'),
							},
						],
					},
				]}
				isCollapsed={isSidebarCollapsed}
				onToggle={() => setIsSidebarCollapsed(!isSidebarCollapsed)}
			/>
			<main className='flex-1 p-6 bg-background-base overflow-auto'>
				{/* Header Section */}
				<div className='mb-8'>
					<div className='flex justify-between items-center mb-4'>
						<div>
							<h1 className='text-600 font-bold text-text-primary mb-2'>
								Projects
							</h1>
							<p className='text-100 text-text-secondary'>
								Manage and track your creative projects with our enhanced design
								system.
							</p>
						</div>
						<div className='flex items-center gap-3'>
							<ThemeToggle />
							<Button
								variant='primary'
								size='md'>
								<PlusIcon className='w-4 h-4 mr-2' />
								New Project
							</Button>
						</div>
					</div>
				</div>

				{/* Projects Grid */}
				<div className='grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6 mb-8'>
					{/* Project Card 1 */}
					<Card
						variant='default'
						size='medium'
						interactive>
						<CardHeader>
							<div className='flex justify-between items-start mb-2'>
								<CardTitle className='text-200 font-semibold'>
									E-commerce Website
								</CardTitle>
								<Badge
									variant='informative'
									size='small'>
									In Progress
								</Badge>
							</div>
							<CardDescription className='text-75 text-text-tertiary'>
								A modern e-commerce platform built with Next.js and Stripe
								integration.
							</CardDescription>
						</CardHeader>
						<CardContent>
							<div className='space-y-3'>
								<div className='flex items-center justify-between text-50 text-text-secondary'>
									<span>Last updated</span>
									<span>2 hours ago</span>
								</div>
								<div className='w-full bg-background-layer-1 rounded-small h-2'>
									<div
										className='h-full bg-accent-background-default rounded-small'
										style={{ width: '75%' }}
									/>
								</div>
							</div>
						</CardContent>
						<CardFooter>
							<div className='flex gap-2 w-full'>
								<Button
									variant='outline'
									size='sm'
									className='flex-1'>
									View
								</Button>
								<Button
									variant='primary'
									size='sm'
									className='flex-1'>
									Edit
								</Button>
							</div>
						</CardFooter>
					</Card>

					{/* Project Card 2 */}
					<Card
						variant='elevated'
						size='medium'
						interactive>
						<CardHeader>
							<div className='flex justify-between items-start mb-2'>
								<CardTitle className='text-200 font-semibold'>
									Mobile App Design
								</CardTitle>
								<Badge
									variant='positive'
									size='small'>
									Completed
								</Badge>
							</div>
							<CardDescription className='text-75 text-text-tertiary'>
								UI/UX design for a fitness tracking mobile application.
							</CardDescription>
						</CardHeader>
						<CardContent>
							<div className='space-y-3'>
								<div className='flex items-center justify-between text-50 text-text-secondary'>
									<span>Last updated</span>
									<span>1 day ago</span>
								</div>
								<div className='w-full bg-background-layer-1 rounded-small h-2'>
									<div
										className='h-full bg-positive-content rounded-small'
										style={{ width: '100%' }}
									/>
								</div>
							</div>
						</CardContent>
						<CardFooter>
							<div className='flex gap-2 w-full'>
								<Button
									variant='outline'
									size='sm'
									className='flex-1'>
									View
								</Button>
								<Button
									variant='primary'
									size='sm'
									className='flex-1'>
									Edit
								</Button>
							</div>
						</CardFooter>
					</Card>

					{/* Project Card 3 */}
					<Card
						variant='outlined'
						size='medium'
						interactive>
						<CardHeader>
							<div className='flex justify-between items-start mb-2'>
								<CardTitle className='text-200 font-semibold'>
									Dashboard Analytics
								</CardTitle>
								<Badge
									variant='notice'
									size='small'>
									Planning
								</Badge>
							</div>
							<CardDescription className='text-75 text-text-tertiary'>
								Real-time analytics dashboard for business intelligence.
							</CardDescription>
						</CardHeader>
						<CardContent>
							<div className='space-y-3'>
								<div className='flex items-center justify-between text-50 text-text-secondary'>
									<span>Last updated</span>
									<span>3 days ago</span>
								</div>
								<div className='w-full bg-background-layer-1 rounded-small h-2'>
									<div
										className='h-full bg-notice-content rounded-small'
										style={{ width: '25%' }}
									/>
								</div>
							</div>
						</CardContent>
						<CardFooter>
							<div className='flex gap-2 w-full'>
								<Button
									variant='outline'
									size='sm'
									className='flex-1'>
									View
								</Button>
								<Button
									variant='primary'
									size='sm'
									className='flex-1'>
									Edit
								</Button>
							</div>
						</CardFooter>
					</Card>
				</div>

				{/* Empty State Card */}
				<Card
					variant='filled'
					size='large'
					className='text-center'>
					<CardContent>
						<div className='py-12'>
							<div className='w-16 h-16 bg-background-layer-2 rounded-large mx-auto mb-4 flex items-center justify-center'>
								<PlusIcon className='w-8 h-8 text-text-tertiary' />
							</div>
							<CardTitle className='text-300 font-semibold mb-2'>
								Create Your First Project
							</CardTitle>
							<CardDescription className='text-100 mb-6 max-w-md mx-auto'>
								Get started by creating a new project. You can organize your
								work, track progress, and collaborate with your team.
							</CardDescription>
							<Button
								variant='primary'
								size='lg'>
								Create Project
							</Button>
						</div>
					</CardContent>
				</Card>
			</main>
		</div>
	);
};

ProjectsPage.getLayout = (page) => {
	return <AppTemplate>{page}</AppTemplate>;
};

export default ProjectsPage;
