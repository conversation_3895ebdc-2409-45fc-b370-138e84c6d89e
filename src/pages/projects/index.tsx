import { ThemeToggle } from '@/components/molecules';
import { SideBar } from '@/components/organisms';
import { AppTemplate } from '@/components/templates';
import { NextPageWithLayout } from '@/pages/_app';
import {
	CircleStackIcon,
	FolderIcon,
	UserCircleIcon,
	PlusIcon,
} from '@heroicons/react/24/outline';
import { useState } from 'react';
import {
	Card,
	CardHeader,
	CardContent,
	CardFooter,
	Button,
	Badge,
	Typography,
} from '@/components/atoms';

const ProjectsPage: NextPageWithLayout = () => {
	const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
	const items = [
		{
			label: 'Project 1',
			icon: <FolderIcon />,
		},
		{
			label: 'Project 2',
			icon: <UserCircleIcon />,
		},
	];
	return (
		<div className='flex h-svh'>
			<SideBar
				title='Projects'
				sections={[
					{
						title: 'My Projects',
						items,
					},
					{
						title: 'Archived Projects',
						items: [
							{
								label: 'Archived Project 1',
								icon: <CircleStackIcon />,
								onClick: () => console.log('Archived Project 1 clicked'),
							},
						],
					},
				]}
				isCollapsed={isSidebarCollapsed}
				onToggle={() => setIsSidebarCollapsed(!isSidebarCollapsed)}
			/>
			<main className='flex-1 p-300 bg-background-base overflow-auto'>
				{/* Header Section */}
				<div className='mb-400'>
					<div className='flex justify-between items-center mb-200'>
						<div>
							<Typography
								variant='h1'
								responsive>
								Projects
							</Typography>
							<Typography
								variant='body-lg'
								color='secondary'
								className='mt-2'>
								Manage and track your creative projects with our enhanced design
								system.
							</Typography>
						</div>
						<div className='flex items-center gap-150'>
							<ThemeToggle />
							<Button
								variant='primary'
								size='md'>
								<PlusIcon className='w-4 h-4 mr-2' />
								New Project
							</Button>
						</div>
					</div>
				</div>

				{/* Projects Grid */}
				<div className='grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-300 mb-400'>
					{/* Project Card 1 */}
					<Card
						variant='default'
						size='medium'
						interactive>
						<CardHeader>
							<div className='flex justify-between items-start mb-100'>
								<Typography
									variant='h4'
									weight='semibold'>
									E-commerce Website
								</Typography>
								<Badge
									variant='informative'
									size='small'>
									In Progress
								</Badge>
							</div>
							<Typography
								variant='body-sm'
								color='tertiary'>
								A modern e-commerce platform built with Next.js and Stripe
								integration.
							</Typography>
						</CardHeader>
						<CardContent>
							<div className='space-y-150'>
								<div className='flex items-center justify-between'>
									<Typography
										variant='caption'
										color='secondary'>
										Last updated
									</Typography>
									<Typography
										variant='caption'
										color='secondary'>
										2 hours ago
									</Typography>
								</div>
								<div className='w-full bg-background-layer-1 rounded-progress h-2'>
									<div
										className='h-full bg-accent-background-default rounded-progress'
										style={{ width: '75%' }}
									/>
								</div>
							</div>
						</CardContent>
						<CardFooter>
							<div className='flex gap-100 w-full'>
								<Button
									variant='outline'
									size='sm'
									className='flex-1'>
									View
								</Button>
								<Button
									variant='primary'
									size='sm'
									className='flex-1'>
									Edit
								</Button>
							</div>
						</CardFooter>
					</Card>

					{/* Project Card 2 */}
					<Card
						variant='elevated'
						size='medium'
						interactive>
						<CardHeader>
							<div className='flex justify-between items-start mb-100'>
								<Typography
									variant='h4'
									weight='semibold'>
									Mobile App Design
								</Typography>
								<Badge
									variant='positive'
									size='small'>
									Completed
								</Badge>
							</div>
							<Typography
								variant='body-sm'
								color='tertiary'>
								UI/UX design for a fitness tracking mobile application.
							</Typography>
						</CardHeader>
						<CardContent>
							<div className='space-y-150'>
								<div className='flex items-center justify-between'>
									<Typography
										variant='caption'
										color='secondary'>
										Last updated
									</Typography>
									<Typography
										variant='caption'
										color='secondary'>
										1 day ago
									</Typography>
								</div>
								<div className='w-full bg-background-layer-1 rounded-progress h-2'>
									<div
										className='h-full bg-positive-content rounded-progress'
										style={{ width: '100%' }}
									/>
								</div>
							</div>
						</CardContent>
						<CardFooter>
							<div className='flex gap-100 w-full'>
								<Button
									variant='outline'
									size='sm'
									className='flex-1'>
									View
								</Button>
								<Button
									variant='primary'
									size='sm'
									className='flex-1'>
									Edit
								</Button>
							</div>
						</CardFooter>
					</Card>

					{/* Project Card 3 */}
					<Card
						variant='outlined'
						size='medium'
						interactive>
						<CardHeader>
							<div className='flex justify-between items-start mb-100'>
								<Typography
									variant='h4'
									weight='semibold'>
									Dashboard Analytics
								</Typography>
								<Badge
									variant='notice'
									size='small'>
									Planning
								</Badge>
							</div>
							<Typography
								variant='body-sm'
								color='tertiary'>
								Real-time analytics dashboard for business intelligence.
							</Typography>
						</CardHeader>
						<CardContent>
							<div className='space-y-150'>
								<div className='flex items-center justify-between'>
									<Typography
										variant='caption'
										color='secondary'>
										Last updated
									</Typography>
									<Typography
										variant='caption'
										color='secondary'>
										3 days ago
									</Typography>
								</div>
								<div className='w-full bg-background-layer-1 rounded-progress h-2'>
									<div
										className='h-full bg-notice-content rounded-progress'
										style={{ width: '25%' }}
									/>
								</div>
							</div>
						</CardContent>
						<CardFooter>
							<div className='flex gap-100 w-full'>
								<Button
									variant='outline'
									size='sm'
									className='flex-1'>
									View
								</Button>
								<Button
									variant='primary'
									size='sm'
									className='flex-1'>
									Edit
								</Button>
							</div>
						</CardFooter>
					</Card>
				</div>

				{/* Empty State Card */}
				<Card
					variant='filled'
					size='large'
					className='text-center'>
					<CardContent>
						<div className='py-600'>
							<div className='w-16 h-16 bg-background-layer-2 rounded-card mx-auto mb-200 flex items-center justify-center'>
								<PlusIcon className='w-8 h-8 text-text-tertiary' />
							</div>
							<Typography
								variant='h3'
								align='center'
								className='mb-2'>
								Create Your First Project
							</Typography>
							<Typography
								variant='body'
								color='secondary'
								align='center'
								className='mb-6 max-w-md mx-auto'>
								Get started by creating a new project. You can organize your
								work, track progress, and collaborate with your team.
							</Typography>
							<Button
								variant='primary'
								size='lg'>
								Create Project
							</Button>
						</div>
					</CardContent>
				</Card>
			</main>
		</div>
	);
};

ProjectsPage.getLayout = (page) => {
	return <AppTemplate>{page}</AppTemplate>;
};

export default ProjectsPage;
