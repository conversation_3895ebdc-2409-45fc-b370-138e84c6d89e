import { AppTemplate } from '@/components/templates';
import { Typography } from '@/components/atoms';
import { NextPageWithLayout } from './_app';

const HomePage: NextPageWithLayout = () => {
	return (
		<main className='p-300'>
			<Typography
				variant='h1'
				responsive>
				Recent projects:
			</Typography>
			<Typography
				variant='body'
				color='secondary'
				className='mt-200'>
				Welcome to PixiGenerator. Your recent projects will appear here.
			</Typography>
		</main>
	);
};

HomePage.getLayout = (page) => {
	return <AppTemplate>{page}</AppTemplate>;
};

export default HomePage;
