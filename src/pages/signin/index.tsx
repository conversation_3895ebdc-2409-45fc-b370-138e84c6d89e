import { AuthTemplate, SignInForm } from '@/components/templates';
import type { NextPageWithLayout } from '../_app';
import Link from 'next/link';

const SignInPage: NextPageWithLayout = () => {
	return (
		<div className='flex w-md min-h-full flex-1 flex-col justify-center px-6 py-12 lg:px-8'>
			<SignInForm />

			<div className='mt-6 text-sm text-muted-foreground'>
				<p>
					Don&apos;t have an account?{' '}
					<Link
						href='register'
						className='font-semibold text-primary hover:underline'>
						Register
					</Link>
				</p>
			</div>
		</div>
	);
};

SignInPage.getLayout = (page) => {
	return <AuthTemplate>{page}</AuthTemplate>;
};

export default SignInPage;
