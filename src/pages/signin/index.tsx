import { AuthTemplate, SignInForm } from '@/components/templates';
import { Typography } from '@/components/atoms';
import type { NextPageWithLayout } from '../_app';
import Link from 'next/link';

const SignInPage: NextPageWithLayout = () => {
	return (
		<div className='flex w-md min-h-full flex-1 flex-col justify-center px-6 py-12 lg:px-8'>
			<SignInForm />

			<div className='mt-6'>
				<Typography
					variant='body-sm'
					color='secondary'>
					Don&apos;t have an account?{' '}
					<Link
						href='register'
						className='font-semibold text-accent-content-default hover:underline'>
						Register
					</Link>
				</Typography>
			</div>
		</div>
	);
};

SignInPage.getLayout = (page) => {
	return <AuthTemplate>{page}</AuthTemplate>;
};

export default SignInPage;
