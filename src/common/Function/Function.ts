import { AuthAPI } from '@/api/AuthApi';
import { getAuthStore } from '@/providers/auth-store-provider';
import { Httpstatus } from '../StandardApi';

interface IRefreshApiResponse {
	status: number;
	data: {
		accessToken: string;
		refreshToken: string;
	};
}

class CommonFunction {
	private static instance: CommonFunction;

	public static getInstance(): CommonFunction {
		if (!CommonFunction.instance) {
			CommonFunction.instance = new CommonFunction();
		}
		return CommonFunction.instance;
	}

	static readonly createHeaders = ({
		withToken = true,
		contentType = 'application/json',
		customToken = '',
	}) => {
		const header: any = {};
		if (contentType === 'application/json') {
			header['Content-Type'] = contentType;
			header['Accept'] = contentType;
		}

		if (withToken) {
			const authStore = getAuthStore();
			const accessToken = authStore.getState().accessToken;
			const token = customToken.length > 0 ? customToken : accessToken;
			if (token) {
				header['Authorization'] = 'Bearer ' + token;
			}
		}
		return header;
	};
	static readonly handleRefresh = async (): Promise<IRefreshApiResponse> => {
		try {
			const authStore = getAuthStore();
			const success = await authStore.getState().refreshTokens();

			if (success) {
				const { accessToken, refreshToken } = authStore.getState();
				return {
					status: Httpstatus.SuccessOK,
					data: {
						accessToken: accessToken || '',
						refreshToken: refreshToken || '',
					},
				};
			} else {
				return {
					status: Httpstatus.Unauthorized,
					data: {
						accessToken: '',
						refreshToken: '',
					},
				};
			}
		} catch (err) {
			console.error('Error refreshing token:', err);
			return {
				status: Httpstatus.Internal,
				data: {
					accessToken: '',
					refreshToken: '',
				},
			};
		}
	};
	static readonly formatIsoDateToFrenshDate = (dateISO: string) => {
		const date = new Date(dateISO);

		const options: Intl.DateTimeFormatOptions = {
			year: 'numeric',
			month: 'numeric',
			day: 'numeric',
			hour: '2-digit',
			minute: '2-digit',
			second: '2-digit',
			timeZone: 'Europe/Paris',
		};
		return date.toLocaleString('fr-FR', options);
	};
}
export { CommonFunction };
