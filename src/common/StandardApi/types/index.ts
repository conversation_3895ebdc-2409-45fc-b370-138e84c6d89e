import { Httpstatus } from '../interface';

export type HeaderObject = {
	Authorization: string;
	'Content-Type': string;
	accept: string;
	[key: string]: string;
};
export interface Refresh<PERSON>son extends Partial<Response> {
	accessToken: string;
	refreshtoken: string;
}
// Standardized API response structure
export interface StandardApiResponse<T = unknown> {
	message: string;
	statusCode: number;
	data?: T;
}

// Legacy interface for backward compatibility during migration
export interface ApiResponse {
	status: Httpstatus;
	data: unknown; // Keep as unknown for backward compatibility, will contain StandardApiResponse
}
