import { ApiResponse, HeaderObject } from "../types";
import { IRefreshApiResponse } from "./IRefreshResponse";

export interface IApi {
  createHeader(headerObject: HeaderObject): Headers;
  get(
    endPoint: string,
    headers: Headers,
    refrechCallback: () => Promise<IRefreshApiResponse>
  ): Promise<ApiResponse>;
  delete(
    endPoint: string,
    headers: Headers,
    refrechCallback: () => Promise<IRefreshApiResponse>
  ): Promise<ApiResponse>;
  post(
    endPoint: string,
    body: any,
    headers: Headers,
    refrechCallback: () => Promise<IRefreshApiResponse>
  ): Promise<ApiResponse>;
  put(
    endPoint: string,
    body: any,
    headers: Headers,
    refrechCallback: () => Promise<IRefreshApiResponse>
  ): Promise<ApiResponse>;
  patch(
    endPoint: string,
    body: any,
    headers: Headers,
    refrechCallback: () => Promise<IRefreshApiResponse>
  ): Promise<ApiResponse>;
}
