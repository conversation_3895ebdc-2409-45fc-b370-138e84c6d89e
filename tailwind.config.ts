import type { Config } from 'tailwindcss';

const config: Config = {
	content: [
		'./src/pages/**/*.{js,ts,jsx,tsx,mdx}',
		'./src/components/**/*.{js,ts,jsx,tsx,mdx}',
		'./src/app/**/*.{js,ts,jsx,tsx,mdx}',
	],
	darkMode: 'class', // Enable class-based dark mode for next-themes
	theme: {
		extend: {
			fontFamily: {
				sans: ['ui-sans-serif', 'system-ui', 'sans-serif'],
				mono: ['ui-monospace', 'monospace'],
			},
			// Map design tokens to Tailwind utilities
			colors: {
				// Background colors
				'background-base': 'var(--color-background-base)',
				'background-layer-1': 'var(--color-background-layer-1)',
				'background-layer-2': 'var(--color-background-layer-2)',

				// Surface colors
				'surface-base': 'var(--color-surface-base)',
				'surface-raised': 'var(--color-surface-raised)',
				'surface-overlay': 'var(--color-surface-overlay)',

				// Text colors
				'text-primary': 'var(--color-text-primary)',
				'text-secondary': 'var(--color-text-secondary)',
				'text-tertiary': 'var(--color-text-tertiary)',
				'text-disabled': 'var(--color-text-disabled)',
				'text-inverse': 'var(--color-text-inverse)',

				// Border colors
				'border-base': 'var(--color-border-base)',
				'border-strong': 'var(--color-border-strong)',
				'border-subtle': 'var(--color-border-subtle)',

				// Interactive colors
				'accent-content-default': 'var(--color-accent-content-default)',
				'accent-content-hover': 'var(--color-accent-content-hover)',
				'accent-content-down': 'var(--color-accent-content-down)',
				'accent-content-focus': 'var(--color-accent-content-focus)',
				'accent-background-default': 'var(--color-accent-background-default)',
				'accent-background-hover': 'var(--color-accent-background-hover)',
				'accent-background-down': 'var(--color-accent-background-down)',
				'accent-border-default': 'var(--color-accent-border-default)',
				'accent-border-hover': 'var(--color-accent-border-hover)',
				'accent-border-focus': 'var(--color-accent-border-focus)',

				// Semantic colors
				'positive-content': 'var(--color-positive-content-default)',
				'positive-background': 'var(--color-positive-background-default)',
				'positive-border': 'var(--color-positive-border-default)',
				'negative-content': 'var(--color-negative-content-default)',
				'negative-background': 'var(--color-negative-background-default)',
				'negative-border': 'var(--color-negative-border-default)',
				'notice-content': 'var(--color-notice-content-default)',
				'notice-background': 'var(--color-notice-background-default)',
				'notice-border': 'var(--color-notice-border-default)',
				'informative-content': 'var(--color-informative-content-default)',
				'informative-background': 'var(--color-informative-background-default)',
				'informative-border': 'var(--color-informative-border-default)',
			},
			spacing: {
				'25': 'var(--spacing-25)',
				'50': 'var(--spacing-50)',
				'75': 'var(--spacing-75)',
				'100': 'var(--spacing-100)',
				'125': 'var(--spacing-125)',
				'150': 'var(--spacing-150)',
				'200': 'var(--spacing-200)',
				'250': 'var(--spacing-250)',
				'300': 'var(--spacing-300)',
				'400': 'var(--spacing-400)',
				'500': 'var(--spacing-500)',
				'600': 'var(--spacing-600)',
				'700': 'var(--spacing-700)',
				'800': 'var(--spacing-800)',
				'900': 'var(--spacing-900)',
				'1000': 'var(--spacing-1000)',
			},
			fontSize: {
				'50': ['var(--font-size-50)', { lineHeight: 'var(--line-height-150)' }],
				'75': ['var(--font-size-75)', { lineHeight: 'var(--line-height-150)' }],
				'100': [
					'var(--font-size-100)',
					{ lineHeight: 'var(--line-height-150)' },
				],
				'200': [
					'var(--font-size-200)',
					{ lineHeight: 'var(--line-height-150)' },
				],
				'300': [
					'var(--font-size-300)',
					{ lineHeight: 'var(--line-height-125)' },
				],
				'400': [
					'var(--font-size-400)',
					{ lineHeight: 'var(--line-height-125)' },
				],
				'500': [
					'var(--font-size-500)',
					{ lineHeight: 'var(--line-height-125)' },
				],
				'600': [
					'var(--font-size-600)',
					{ lineHeight: 'var(--line-height-125)' },
				],
				'700': [
					'var(--font-size-700)',
					{ lineHeight: 'var(--line-height-100)' },
				],
				'800': [
					'var(--font-size-800)',
					{ lineHeight: 'var(--line-height-100)' },
				],
				'900': [
					'var(--font-size-900)',
					{ lineHeight: 'var(--line-height-100)' },
				],
			},
			fontWeight: {
				light: 'var(--font-weight-light)',
				regular: 'var(--font-weight-regular)',
				medium: 'var(--font-weight-medium)',
				semibold: 'var(--font-weight-semibold)',
				bold: 'var(--font-weight-bold)',
				extrabold: 'var(--font-weight-extrabold)',
			},
			borderRadius: {
				none: 'var(--border-radius-none)',
				small: 'var(--border-radius-small)',
				medium: 'var(--border-radius-medium)',
				large: 'var(--border-radius-large)',
				'extra-large': 'var(--border-radius-extra-large)',
				full: 'var(--border-radius-full)',
			},
			borderWidth: {
				none: 'var(--border-width-none)',
				thin: 'var(--border-width-thin)',
				thick: 'var(--border-width-thick)',
				thicker: 'var(--border-width-thicker)',
			},
			boxShadow: {
				none: 'var(--shadow-none)',
				small: 'var(--shadow-small)',
				medium: 'var(--shadow-medium)',
				large: 'var(--shadow-large)',
				'extra-large': 'var(--shadow-extra-large)',
				inner: 'var(--shadow-inner)',
			},
			height: {
				'button-small': 'var(--size-button-height-small)',
				'button-medium': 'var(--size-button-height-medium)',
				'button-large': 'var(--size-button-height-large)',
				'input-small': 'var(--size-input-height-small)',
				'input-medium': 'var(--size-input-height-medium)',
				'input-large': 'var(--size-input-height-large)',
			},
		},
	},
	plugins: [],
};

export default config;
