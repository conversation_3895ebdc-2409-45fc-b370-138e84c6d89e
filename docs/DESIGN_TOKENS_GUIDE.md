# Design Tokens Configuration Guide

This guide explains how to easily customize the entire app's appearance by modifying design token default values.

## 🎯 Quick Start

All design token defaults are located in `src/styles/design-tokens.css`. Simply modify the values in this file to change the entire app's appearance.

## 🎨 Changing the Primary Color

To change the app's primary color, modify these values in `design-tokens.css`:

```css
:root {
  --primary-hue: 145;        /* Change this number (0-360) */
  --primary-saturation: 0.125; /* Color intensity (0-1) */
  --primary-lightness: 55%;    /* Brightness (0-100%) */
}
```

### Popular Color Examples:
- **Blue**: `--primary-hue: 220;`
- **Purple**: `--primary-hue: 270;`
- **Red**: `--primary-hue: 0;`
- **Orange**: `--primary-hue: 30;`
- **Teal**: `--primary-hue: 180;`

## 🔄 Adjusting Rounded Corners

To make the app more or less rounded, modify these values:

```css
:root {
  --radius-scale-small: 0.5rem;    /* Small elements */
  --radius-scale-medium: 0.75rem;  /* Default elements */
  --radius-scale-large: 1rem;      /* Buttons, badges */
  --radius-scale-xl: 1.25rem;      /* Cards, containers */
}
```

### Preset Themes:

**More Rounded:**
```css
--radius-scale-small: 0.75rem;
--radius-scale-medium: 1rem;
--radius-scale-large: 1.5rem;
--radius-scale-xl: 2rem;
```

**Less Rounded:**
```css
--radius-scale-small: 0.25rem;
--radius-scale-medium: 0.375rem;
--radius-scale-large: 0.5rem;
--radius-scale-xl: 0.75rem;
```

## 📏 Spacing Adjustments

To make the app more compact or spacious:

```css
:root {
  --spacing-scale-xs: 0.125rem;    /* 2px */
  --spacing-scale-sm: 0.25rem;     /* 4px */
  --spacing-scale-base: 0.5rem;    /* 8px */
  --spacing-scale-md: 0.75rem;     /* 12px */
  --spacing-scale-lg: 1rem;        /* 16px */
  --spacing-scale-xl: 1.5rem;      /* 24px */
  --spacing-scale-2xl: 2rem;       /* 32px */
}
```

## 📝 Typography Scale

To adjust text sizes throughout the app:

```css
:root {
  --text-scale-xs: 0.75rem;        /* 12px */
  --text-scale-sm: 0.875rem;       /* 14px */
  --text-scale-base: 1rem;         /* 16px */
  --text-scale-lg: 1.125rem;       /* 18px */
  --text-scale-xl: 1.25rem;        /* 20px */
  --text-scale-2xl: 1.5rem;        /* 24px */
  --text-scale-3xl: 1.875rem;      /* 30px */
  --text-scale-4xl: 2.25rem;       /* 36px */
}
```

## 🎭 Component-Specific Defaults

Each component type has its own default radius that you can customize:

```css
:root {
  --radius-button: var(--radius-scale-large);    /* Button corners */
  --radius-card: var(--radius-scale-xl);         /* Card corners */
  --radius-input: var(--radius-scale-medium);    /* Input corners */
  --radius-badge: var(--radius-scale-large);     /* Badge corners */
  --radius-progress: var(--radius-scale-small);  /* Progress bar corners */
}
```

## 🚀 Quick Theme Presets

The `design-tokens.css` file includes commented preset themes. Simply uncomment one to apply:

### Available Presets:
1. **More Rounded Theme** - Softer, more rounded appearance
2. **Less Rounded Theme** - Sharper, more angular appearance  
3. **Blue Primary Theme** - Blue accent color
4. **Purple Primary Theme** - Purple accent color
5. **Compact Spacing Theme** - Tighter spacing throughout
6. **Spacious Theme** - More generous spacing

## 🔧 How It Works

The design token system uses CSS custom properties with a hierarchical structure:

1. **Scale Values** - Base measurements (e.g., `--radius-scale-large`)
2. **Component Defaults** - Component-specific tokens (e.g., `--radius-button`)
3. **Tailwind Classes** - Utility classes that use the tokens (e.g., `rounded-button`)

When you change a scale value, it automatically updates all components that use it.

## 📱 Component Mapping

Here's how the tokens map to components:

| Component | Radius Token | Usage |
|-----------|--------------|-------|
| Cards | `rounded-card` | All card containers |
| Buttons | `rounded-button` | All button elements |
| Inputs | `rounded-input` | Form inputs |
| Badges | `rounded-badge` | Status badges |
| Progress Bars | `rounded-progress` | Progress indicators |
| Default | `rounded` | General elements |

## 🎨 Examples

### Making a More Rounded App:
1. Open `src/styles/design-tokens.css`
2. Uncomment the "More Rounded Theme" section
3. Save the file
4. The entire app will now use larger border radius values

### Changing to Blue Primary:
1. Open `src/styles/design-tokens.css`
2. Uncomment the "Blue Primary Theme" section
3. Save the file
4. All accent colors will now be blue instead of green

### Custom Adjustments:
You can mix and match or create your own values:

```css
:root {
  /* Custom purple with extra rounded corners */
  --primary-hue: 280;
  --radius-scale-large: 2rem;
  --radius-scale-xl: 2.5rem;
}
```

## 🔄 Live Updates

Changes to design tokens take effect immediately in development mode. Simply:
1. Edit the values in `design-tokens.css`
2. Save the file
3. See the changes reflected instantly in your browser

This system makes it incredibly easy to experiment with different visual styles and maintain consistency across your entire application!
