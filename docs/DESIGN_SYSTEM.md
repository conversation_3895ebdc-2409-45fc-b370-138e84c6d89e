# Design System Documentation

This document outlines our enhanced design system following Adobe Spectrum design token principles with OKCH color space implementation.

## Overview

Our design system is built on a foundation of semantic design tokens that provide consistency, scalability, and maintainability across the application. The system follows Adobe Spectrum's hierarchical approach to design tokens and uses the OKCH color space for better perceptual uniformity and wider color gamut support.

## Key Features

- **OKCH Color Space**: Perceptually uniform colors for better accessibility and consistency
- **Semantic Token Architecture**: Three-tier token system (Global → Semantic → Component)
- **Adobe Spectrum Compliance**: Following industry-standard design token guidelines
- **Dark Mode Support**: Automatic theme switching with proper contrast ratios
- **Accessibility First**: WCAG AA compliant color contrasts and focus states

## Design Token Architecture

### Token Hierarchy

1. **Global Tokens** - Base color palette using OKCH color space and primitive values
2. **Semantic Tokens** - Context-aware tokens that map to global tokens
3. **Component Tokens** - Component-specific tokens for specialized use cases

### OKCH Color System

Our color system uses the OKCH color space (Oklch in CSS) which provides:
- **Perceptual Uniformity**: Colors with the same lightness appear equally bright
- **Predictable Behavior**: Easier to create consistent color scales
- **Better Accessibility**: More reliable contrast ratio calculations
- **Future-Proof**: Wide color gamut support for modern displays

Example OKCH color definition:
```css
--color-blue-500: oklch(55% 0.155 250);
/* Lightness: 55%, Chroma: 0.155, Hue: 250° */
```

### Color System

#### Background Colors
- `background-base` - Primary background color
- `background-layer-1` - First elevation layer
- `background-layer-2` - Second elevation layer

#### Surface Colors
- `surface-base` - Default surface color for cards and containers
- `surface-raised` - Elevated surface color
- `surface-overlay` - Overlay surface color for modals and popovers

#### Text Colors
- `text-primary` - Primary text color for headings and important content
- `text-secondary` - Secondary text color for body text
- `text-tertiary` - Tertiary text color for captions and metadata
- `text-disabled` - Disabled text color
- `text-inverse` - Inverse text color for dark backgrounds

#### Border Colors
- `border-base` - Default border color
- `border-strong` - Strong border color for emphasis
- `border-subtle` - Subtle border color for light separation

#### Interactive Colors
- `accent-content-default` - Default accent color for interactive elements
- `accent-content-hover` - Hover state for accent elements
- `accent-content-down` - Active/pressed state for accent elements
- `accent-background-default` - Default accent background
- `accent-border-default` - Default accent border

#### Semantic Status Colors
- `positive-*` - Success states (green)
- `negative-*` - Error states (red)
- `notice-*` - Warning states (orange)
- `informative-*` - Information states (blue)

### Spacing System

Based on a 4px grid system:
- `spacing-25` - 2px
- `spacing-50` - 4px
- `spacing-75` - 6px
- `spacing-100` - 8px
- `spacing-150` - 12px
- `spacing-200` - 16px
- `spacing-300` - 24px
- `spacing-400` - 32px
- `spacing-500` - 40px
- `spacing-600` - 48px
- `spacing-800` - 64px
- `spacing-1000` - 80px

### Typography Scale

- `font-size-50` - 12px (Small text, captions)
- `font-size-75` - 14px (Body text, labels)
- `font-size-100` - 16px (Default body text)
- `font-size-200` - 18px (Large body text)
- `font-size-300` - 20px (Small headings)
- `font-size-400` - 24px (Medium headings)
- `font-size-500` - 30px (Large headings)
- `font-size-600` - 36px (Display text)
- `font-size-700` - 48px (Hero text)

### Border Radius

- `border-radius-small` - 4px
- `border-radius-medium` - 6px
- `border-radius-large` - 8px
- `border-radius-extra-large` - 12px
- `border-radius-full` - 9999px (circular)

### Shadows

- `shadow-small` - Subtle elevation
- `shadow-medium` - Standard elevation
- `shadow-large` - High elevation
- `shadow-extra-large` - Maximum elevation

## Components

### Badge Component

The Badge component is a versatile labeling component that supports semantic variants and interactive states.

#### Variants
- `neutral` - Default neutral styling
- `accent` - Primary brand color
- `positive` - Success/positive states (green)
- `negative` - Error/negative states (red)
- `notice` - Warning/attention states (orange)
- `informative` - Information states (blue)

#### Emphasis Levels
- `subtle` - Low emphasis with background color
- `strong` - High emphasis with solid background
- `outline` - Medium emphasis with border only

#### Sizes
- `small` - Compact size (12px text, 2px padding)
- `medium` - Standard size (14px text, 4px padding)
- `large` - Large size (16px text, 6px padding)

#### Features
- **Icon Support**: Optional leading icon
- **Removable**: Optional close button with callback
- **Accessible**: Proper ARIA labels and keyboard support

#### Usage Example

```tsx
import { Badge } from '@/components/atoms';
import { CheckCircleIcon } from '@heroicons/react/24/outline';

// Basic badge
<Badge variant="positive" size="small">Completed</Badge>

// Badge with icon
<Badge variant="informative" icon={<CheckCircleIcon />}>
  In Progress
</Badge>

// Removable badge
<Badge
  variant="neutral"
  removable
  onRemove={() => handleRemove()}
>
  Tag Name
</Badge>
```

### Card Component

The Card component is a flexible container that supports multiple variants and interactive states.

#### Variants
- `default` - Standard card with border and subtle shadow
- `outlined` - Card with prominent border, no shadow
- `elevated` - Card with no border, prominent shadow
- `filled` - Card with filled background, no border or shadow

#### Sizes
- `small` - Compact padding (16px)
- `medium` - Standard padding (24px)
- `large` - Spacious padding (32px)

#### Sub-components
- `CardHeader` - Header section with title and description
- `CardContent` - Main content area
- `CardFooter` - Footer section for actions
- `CardTitle` - Semantic title component
- `CardDescription` - Semantic description component

#### Usage Example

```tsx
import { Card, CardHeader, CardContent, CardFooter, CardTitle, CardDescription, Button } from '@/components/atoms';

<Card variant="elevated" size="medium" interactive>
  <CardHeader>
    <CardTitle>Project Title</CardTitle>
    <CardDescription>Project description goes here</CardDescription>
  </CardHeader>
  <CardContent>
    <p>Main content area</p>
  </CardContent>
  <CardFooter>
    <Button variant="outline" size="sm">Cancel</Button>
    <Button variant="primary" size="sm">Save</Button>
  </CardFooter>
</Card>
```

### Sidebar Component

The Sidebar component provides navigation with collapsible functionality and semantic design tokens.

#### Features
- **Collapsible**: Smooth animation between expanded and collapsed states
- **Semantic HTML**: Uses proper `<aside>`, `<nav>`, and `<section>` elements
- **Accessibility**: ARIA labels, keyboard navigation, and focus management
- **Design Tokens**: Uses semantic spacing, colors, and typography tokens
- **Badge Support**: Navigation items can include badges for notifications

#### Sub-components
- `SidebarHeader` - Header with title, subtitle, and toggle button
- `SidebarSection` - Grouped navigation items with optional title
- `SidebarItem` - Individual navigation item with icon, label, and optional badge

#### Improvements Made
1. **Removed non-null assertions** - Safer type handling
2. **Added proper semantic HTML** - Better accessibility
3. **Consistent design tokens** - Uses new spacing and color tokens
4. **Enhanced focus states** - Proper keyboard navigation
5. **Badge integration** - Support for notification badges
6. **Better responsive behavior** - Smooth transitions and proper sizing

#### Usage Example

```tsx
import { Sidebar } from '@/components/organisms';
import { Badge } from '@/components/atoms';

const sidebarSections = [
  {
    title: 'Main',
    items: [
      {
        icon: <HomeIcon />,
        label: 'Dashboard',
        isActive: true,
        onClick: () => navigate('/dashboard')
      },
      {
        icon: <ProjectIcon />,
        label: 'Projects',
        badge: <Badge variant="informative" size="small">3</Badge>,
        onClick: () => navigate('/projects')
      }
    ]
  }
];

<Sidebar
  title="App Name"
  subtitle="v1.0.0"
  sections={sidebarSections}
  isCollapsed={isCollapsed}
  onToggle={() => setIsCollapsed(!isCollapsed)}
/>
```

## Tailwind Integration

All design tokens are mapped to Tailwind CSS utilities for easy usage:

### Color Classes
```css
bg-background-base
text-text-primary
border-border-base
bg-accent-background-default
text-positive-content
```

### Spacing Classes
```css
p-200    /* padding: 16px */
m-300    /* margin: 24px */
gap-150  /* gap: 12px */
```

### Typography Classes
```css
text-100  /* 16px font size */
text-300  /* 20px font size */
font-semibold
```

### Border Radius Classes
```css
rounded-small   /* 4px */
rounded-medium  /* 6px */
rounded-large   /* 8px */
```

## Dark Mode Support

The design system automatically adapts to dark mode using CSS custom properties. All semantic tokens are redefined for dark mode contexts.

## Best Practices

1. **Use semantic tokens** instead of global tokens in components
2. **Follow the spacing scale** for consistent layouts
3. **Use appropriate text colors** for hierarchy and accessibility
4. **Leverage interactive states** for better user experience
5. **Test in both light and dark modes** to ensure proper contrast

## Migration Guide

When updating existing components:

1. Replace hardcoded colors with semantic tokens
2. Use the new spacing scale instead of arbitrary values
3. Update typography to use the new scale
4. Replace custom shadows with design system shadows
5. Use the new border radius tokens for consistency

## Accessibility

The design system ensures:
- Sufficient color contrast ratios (WCAG AA compliant)
- Consistent focus states for keyboard navigation
- Semantic color usage for status indicators
- Scalable typography for readability
